// Test Family Type Database Connections
// This script tests the exact same connections that the Family Type component uses

import { createClient } from '@supabase/supabase-js';

// Test the environment configuration
const CRM_URL = process.env.VITE_SUPABASE_URL_CRM || 'https://tlfwcnikdlwoliqzavxj.supabase.co';
const CRM_KEY = process.env.VITE_SUPABASE_ANON_KEY_CRM || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRsZndjbmlrZGx3b2xpcXphdnhqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQ4NTcwMjgsImV4cCI6MjA2MDQzMzAyOH0.fCaJNbHL6VwKxTbt3vYl2F5O2gRoMFuUO1bhqEtSWpI';

const QUOTE_URL = process.env.VITE_SUPABASE_URL_QUOTE || 'https://lkqbrlrmrsnbtkoryazq.supabase.co';
const QUOTE_KEY = process.env.VITE_SUPABASE_ANON_KEY_QUOTE || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxrcWJybHJtcnNuYnRrb3J5YXpxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU0MDA2ODYsImV4cCI6MjA2MDk3NjY4Nn0.0E4Z87L9j32k3jKa15n4LpmFsVx8YCJuwovi-mSw4SE';

console.log('🔍 TESTING FAMILY TYPE DATABASE CONNECTIONS...\n');

console.log('📊 Environment Configuration:');
console.log('CRM URL:', CRM_URL);
console.log('Quote URL:', QUOTE_URL);
console.log('');

// Create clients
const crmClient = createClient(CRM_URL, CRM_KEY);
const quoteClient = createClient(QUOTE_URL, QUOTE_KEY);

async function testConnections() {
  console.log('🎯 TESTING FAMILY TYPE COMPONENT WORKFLOW...\n');

  // Test 1: Fetch family types from CRM (like fetchFamilyTypes function)
  console.log('1️⃣ Testing fetchFamilyTypes from CRM database...');
  try {
    const { data: familyTypes, error: ftError } = await crmClient
      .from('family_type')
      .select('*')
      .order('family_id');

    if (ftError) {
      console.error('❌ Error fetching family types:', ftError.message);
    } else {
      console.log(`✅ Successfully loaded ${familyTypes?.length || 0} family types from CRM database`);
      if (familyTypes && familyTypes.length > 0) {
        console.log('   Sample family types:', familyTypes.slice(0, 3).map(ft => ft.family_type));
      }
    }
  } catch (error) {
    console.error('❌ Exception fetching family types:', error.message);
  }

  console.log('');

  // Test 2: Fetch baseline quotes from Quote DB (like fetchBaselineQuotes function)
  console.log('2️⃣ Testing fetchBaselineQuotes from Quote database...');
  try {
    const { data: quotes, error: qError } = await quoteClient
      .from('quotes')
      .select('id, package_name, customer_name, destination, family_type, total_cost, no_of_persons, children, infants, extra_adults, is_draft')
      .not('total_cost', 'is', null)
      .gt('total_cost', 0)
      .order('created_at', { ascending: false })
      .limit(5);

    if (qError) {
      console.error('❌ Error fetching baseline quotes:', qError.message);
    } else {
      console.log(`✅ Successfully fetched ${quotes?.length || 0} quotes from Quote database`);
      if (quotes && quotes.length > 0) {
        console.log('   Sample quotes:', quotes.slice(0, 2).map(q => `${q.customer_name} - ${q.destination}`));
      }
    }
  } catch (error) {
    console.error('❌ Exception fetching baseline quotes:', error.message);
  }

  console.log('');

  // Test 3: Check family_type_prices table access (like loadFamilyTypePricesFromDatabase)
  console.log('3️⃣ Testing family_type_prices table access...');
  try {
    const { data: familyPrices, error: fpError } = await quoteClient
      .from('family_type_prices')
      .select('id, family_type_name, total_price')
      .limit(5);

    if (fpError) {
      console.error('❌ Error accessing family_type_prices:', fpError.message);
    } else {
      console.log(`✅ Successfully accessed family_type_prices table with ${familyPrices?.length || 0} records`);
      if (familyPrices && familyPrices.length > 0) {
        console.log('   Sample prices:', familyPrices.slice(0, 2).map(fp => `${fp.family_type_name}: ₹${fp.total_price}`));
      }
    }
  } catch (error) {
    console.error('❌ Exception accessing family_type_prices:', error.message);
  }

  console.log('');

  // Test 4: Check quote_mappings table access
  console.log('4️⃣ Testing quote_mappings table access...');
  try {
    const { data: quoteMappings, error: qmError } = await quoteClient
      .from('quote_mappings')
      .select('id, quote_name, customer_name')
      .limit(3);

    if (qmError) {
      console.error('❌ Error accessing quote_mappings:', qmError.message);
    } else {
      console.log(`✅ Successfully accessed quote_mappings table with ${quoteMappings?.length || 0} records`);
      if (quoteMappings && quoteMappings.length > 0) {
        console.log('   Sample mappings:', quoteMappings.slice(0, 2).map(qm => `${qm.customer_name} - ${qm.quote_name}`));
      }
    }
  } catch (error) {
    console.error('❌ Exception accessing quote_mappings:', error.message);
  }

  console.log('\n✅ Family Type database connection test complete!');
  console.log('\n📋 SUMMARY:');
  console.log('- family_type table: CRM Database ✅');
  console.log('- quotes table: Quote Database ✅');
  console.log('- family_type_prices table: Quote Database ✅');
  console.log('- quote_mappings table: Quote Database ✅');
}

// Run the test
testConnections().catch(console.error);
