import { getQuoteClient } from '../../lib/supabaseManager';

// Interfaces matching the Quote Generator
interface HotelRow {
  hotelName: string;
  roomType: string;
  price: number;
  mealPlan: string;
  noOfRooms: number;
  stayNights: number;
  extraAdultCost: number;
  childrenCost: number;
  infantCost: number;
  gstType: '0' | '12' | '18' | 'NET' | 'EXC';
  tacPercentage: number;
  tacAmount: number;
  info: string;
  stayPrice: number;
  gstAmount: number;
  currency?: string;
}

interface CostGroups {
  basicCosts: {
    meals: number;
    transportation: number;
    cabSightseeing: number;
    trainCost: number;
    ferryCost: number;
    parkingToll: number;
  };
  addOnCosts: {
    addOnActivity: number;
    marketing: number;
    addOn: number;
  };
  optionalCosts: {
    flightTicket: number;
    guideWages: number;
  };
}

interface BaselineQuoteData {
  id: string;
  package_name: string;
  customer_name: string;
  destination: string;
  family_type: string;
  total_cost: number;
  no_of_persons: number;
  children: number;
  infants: number;
  extra_adults: number;
  commission_rate?: number;
  custom_commission?: number;
  // Complete quote data
  hotelRows: HotelRow[];
  costs: CostGroups;
  discountAmount?: number;
  gstRate?: number;
}

interface FamilyType {
  id: string;
  name: string;
  description: string;
  adults: number;
  infants: number;
  children: number;
  teenagers: number;
  grandparents: number;
  totalCount: number;
  cabType?: string;
  cab_type?: string;
  rooms?: number;
  rooms_need?: number;
  // Original Supabase fields
  family_id?: string;
  family_type?: string;
  no_of_adults?: number;
  no_of_infants?: number;
  no_of_child?: number;
  no_of_children?: number;
  family_count?: number;
  calculatedPrice?: number;
  cab_capacity?: number;
}

interface PriceCalculationResult {
  basePrice: number;
  extraCosts: number;
  subtotal: number;
  gstAmount: number;
  tacAmount: number;
  finalPrice: number;
  showGstSeparately: boolean;
}

interface RoomOccupancyRules {
  standardRoom: { maxAdults: number; maxChildrenUnder5: number; maxExtraAdults: number };
  tripleRoom: { maxAdults: number; maxChildrenUnder5: number; maxExtraAdults: number };
  familyRoom: { maxAdults: number; maxChildrenUnder5: number; maxExtraAdults: number };
}

// Hotel room occupancy rules based on your specifications
const ROOM_OCCUPANCY_RULES: RoomOccupancyRules = {
  standardRoom: { maxAdults: 2, maxChildrenUnder5: 2, maxExtraAdults: 1 },
  tripleRoom: { maxAdults: 3, maxChildrenUnder5: 2, maxExtraAdults: 1 },
  familyRoom: { maxAdults: 4, maxChildrenUnder5: 2, maxExtraAdults: 1 }
};



// Calculate hotel stay price using the same logic as Quote Generator
const calculateHotelStayPrice = (row: HotelRow, extraAdultsCount: number, childrenCount: number, infantsCount: number = 0): PriceCalculationResult => {
  // Basic price calculation
  const roomNightPrice = row.price * row.noOfRooms * row.stayNights;

  // Extra adults cost
  const extraAdultTotal = row.extraAdultCost * extraAdultsCount * row.stayNights;

  // Children cost (children > 5 years)
  const childrenTotal = row.childrenCost * childrenCount * row.stayNights;

  // Infants cost (usually free, but included for completeness)
  const infantTotal = row.infantCost * infantsCount * row.stayNights;

  // Base price is room nights plus extras
  const basePrice = roomNightPrice;
  const extraCosts = extraAdultTotal + childrenTotal + infantTotal;

  // Subtotal before GST and TAC
  const subtotal = basePrice + extraCosts;

  // GST calculation based on type
  let gstAmount = 0;
  if (row.gstType !== 'NET' && row.gstType !== 'EXC') {
    gstAmount = (subtotal * Number(row.gstType)) / 100;
  }

  // TAC calculation
  const tacAmount = (subtotal * row.tacPercentage) / 100;

  // Final price calculation based on GST type
  let finalPrice = subtotal + tacAmount;
  if (row.gstType !== 'EXC') {
    finalPrice += gstAmount;
  }

  return {
    basePrice,
    extraCosts,
    subtotal,
    gstAmount,
    tacAmount,
    finalPrice,
    showGstSeparately: row.gstType === 'EXC'
  };
};

// Calculate room requirements based on family composition
const calculateRoomRequirements = (familyType: FamilyType): { 
  roomsNeeded: number; 
  extraAdults: number; 
  childrenCharged: number; 
  infantsFree: number;
  roomType: string;
} => {
  const adults = familyType.adults || familyType.no_of_adults || 0;
  const totalChildren = familyType.children || familyType.no_of_children || familyType.no_of_child || 0;
  const infants = familyType.infants || familyType.no_of_infants || 0;
  
  // Split children into free (≤5) and chargeable (6-12)
  // Assuming 70% of children are under 5 and free, 30% are 6-12 and chargeable
  const childrenUnder5 = Math.floor(totalChildren * 0.7);
  const children6to12 = totalChildren - childrenUnder5;

  let roomsNeeded = 1;
  let extraAdults = 0;
  let roomType = 'Standard';

  // Calculate room requirements based on adults
  if (adults <= 2) {
    roomsNeeded = 1;
    roomType = 'Standard';
    extraAdults = 0;
  } else if (adults === 3) {
    roomsNeeded = 1;
    roomType = 'Triple';
    extraAdults = 0;
  } else if (adults === 4) {
    roomsNeeded = 1;
    roomType = 'Family';
    extraAdults = 0;
  } else if (adults === 5) {
    roomsNeeded = 1;
    roomType = 'Family';
    extraAdults = 1; // 4 adults in family room + 1 extra adult
  } else {
    // More than 5 adults - need multiple rooms
    roomsNeeded = Math.ceil(adults / 2);
    roomType = 'Standard';
    extraAdults = Math.max(0, adults - (roomsNeeded * 2));
  }

  // Adjust for children if needed
  if (childrenUnder5 > 2 * roomsNeeded) {
    // Too many children for the rooms - might need additional room
    const additionalRoomsForChildren = Math.ceil((childrenUnder5 - (2 * roomsNeeded)) / 2);
    roomsNeeded += additionalRoomsForChildren;
  }

  return {
    roomsNeeded,
    extraAdults,
    childrenCharged: children6to12,
    infantsFree: infants + childrenUnder5, // Both infants and children ≤5 are free
    roomType
  };
};

// Fetch complete baseline quote data including hotel rows and costs
export const fetchCompleteQuoteData = async (quoteId: string): Promise<BaselineQuoteData | null> => {
  try {
    if (!supabase) {
      console.error('Supabase client not available');
      return null;
    }

    // Fetch quote basic data
    const { data: quoteData, error: quoteError } = await supabase
      .from('quotes')
      .select('*')
      .eq('id', quoteId)
      .single();

    if (quoteError) {
      console.error('Error fetching quote:', quoteError);
      return null;
    }

    // Fetch hotel rows
    const { data: hotelRowsData, error: hotelError } = await supabase
      .from('hotel_rows')
      .select('*')
      .eq('quote_id', quoteId);

    if (hotelError) {
      console.error('Error fetching hotel rows:', hotelError);
      return null;
    }

    // Fetch costs
    const { data: costsData, error: costsError } = await supabase
      .from('quote_costs')
      .select('*')
      .eq('quote_id', quoteId)
      .single();

    if (costsError) {
      console.error('Error fetching costs:', costsError);
    }

    // Transform hotel rows data
    const hotelRows: HotelRow[] = (hotelRowsData || []).map(row => ({
      hotelName: row.hotel_name || '',
      roomType: row.room_type || 'Standard',
      price: row.price || 0,
      mealPlan: row.meal_plan || 'MAP',
      noOfRooms: row.no_of_rooms || 0,
      stayNights: row.stay_nights || 0,
      extraAdultCost: row.extra_adult_cost || 0,
      childrenCost: row.children_cost || 0,
      infantCost: row.infant_cost || 0,
      gstType: row.gst_type || '0',
      tacPercentage: row.tac_percentage || 0,
      tacAmount: row.tac_amount || 0,
      info: row.info || '',
      stayPrice: row.stay_price || 0,
      gstAmount: row.gst_amount || 0,
      currency: row.currency || 'INR'
    }));

    // Transform costs data
    const costs: CostGroups = {
      basicCosts: {
        meals: costsData?.meals || 0,
        transportation: costsData?.transportation || 0,
        cabSightseeing: costsData?.cab_sightseeing || 0,
        trainCost: costsData?.train_cost || 0,
        ferryCost: costsData?.ferry_cost || 0,
        parkingToll: costsData?.parking_toll || 0,
      },
      addOnCosts: {
        addOnActivity: costsData?.add_on_activity || 0,
        marketing: costsData?.marketing || 0,
        addOn: costsData?.add_on || 0,
      },
      optionalCosts: {
        flightTicket: costsData?.flight_ticket || 0,
        guideWages: costsData?.guide_wages || 0,
      }
    };

    return {
      id: quoteData.id,
      package_name: quoteData.package_name || '',
      customer_name: quoteData.customer_name || '',
      destination: quoteData.destination || '',
      family_type: quoteData.family_type || '',
      total_cost: quoteData.total_cost || 0,
      no_of_persons: quoteData.no_of_persons || 0,
      children: quoteData.children || 0,
      infants: quoteData.infants || 0,
      extra_adults: quoteData.extra_adults || 0,
      commission_rate: quoteData.commission_rate || 5,
      custom_commission: quoteData.custom_commission || 0,
      hotelRows,
      costs,
      discountAmount: 0, // Can be added later if needed
      gstRate: 0.05 // 5% GST
    };

  } catch (error) {
    console.error('Exception fetching complete quote data:', error);
    return null;
  }
};

// Calculate family type price using the exact Quote Generator logic
export const calculateFamilyTypePrice = (familyType: FamilyType, baselineQuote: BaselineQuoteData): number => {
  if (!baselineQuote || !baselineQuote.total_cost) return 0;

  // Calculate room requirements for the family type
  const roomReq = calculateRoomRequirements(familyType);

  // Calculate hotel costs for this family type
  const familyHotelCosts = baselineQuote.hotelRows.map(row => {
    // Adjust the hotel row for the new family composition
    const adjustedRow: HotelRow = {
      ...row,
      noOfRooms: roomReq.roomsNeeded
    };

    return calculateHotelStayPrice(
      adjustedRow, 
      roomReq.extraAdults, 
      roomReq.childrenCharged, 
      roomReq.infantsFree
    );
  });

  // Sum up all hotel costs
  const totalHotelCost = familyHotelCosts.reduce((sum, cost) => sum + cost.finalPrice, 0);

  // Calculate other costs (these remain proportional to the baseline)
  const baselineAdults = (baselineQuote.no_of_persons || 0) + (baselineQuote.extra_adults || 0);
  const familyAdults = familyType.adults || familyType.no_of_adults || 0;
  const adultRatio = baselineAdults > 0 ? familyAdults / baselineAdults : 1;

  // Scale other costs based on number of adults and apply vehicle multiplier for transportation
  const vehicleMultiplier = getVehicleTypeMultiplier(familyType);
  
  // Apply vehicle multiplier to transportation-related costs
  const scaledTransportationCosts = (
    (baselineQuote.costs.basicCosts.transportation || 0) * adultRatio * vehicleMultiplier +
    (baselineQuote.costs.basicCosts.cabSightseeing || 0) * adultRatio * vehicleMultiplier
  );
  
  // Other basic costs scale normally
  const scaledOtherBasicCosts = (
    (baselineQuote.costs.basicCosts.meals || 0) * adultRatio +
    (baselineQuote.costs.basicCosts.trainCost || 0) * adultRatio +
    (baselineQuote.costs.basicCosts.ferryCost || 0) * adultRatio +
    (baselineQuote.costs.basicCosts.parkingToll || 0) * adultRatio
  );
  
  const scaledBasicCosts = scaledTransportationCosts + scaledOtherBasicCosts;
  const scaledAddOnCosts = Object.values(baselineQuote.costs.addOnCosts).reduce((sum, cost) => sum + (cost * adultRatio), 0);
  const scaledOptionalCosts = Object.values(baselineQuote.costs.optionalCosts).reduce((sum, cost) => sum + (cost * adultRatio), 0);

  const otherCostsTotal = scaledBasicCosts + scaledAddOnCosts + scaledOptionalCosts;

  // Calculate initial subtotal
  const initialSubtotal = totalHotelCost + otherCostsTotal;

  // Apply discount (if any)
  const discountAmount = baselineQuote.discountAmount || 0;
  const subtotal = initialSubtotal - discountAmount;

  // Calculate commission
  const commissionRate = baselineQuote.commission_rate || 5;
  const commissionAmount = subtotal * (commissionRate / 100);
  const totalWithCommission = subtotal + commissionAmount;

  // Calculate GST
  const gstRate = baselineQuote.gstRate || 0.05;
  const gst = totalWithCommission * gstRate;
  const grandTotal = totalWithCommission + gst;

  return Math.round(Math.max(0, grandTotal));
};

// Enhanced calculation with detailed breakdown
export const calculateFamilyTypePriceDetailed = (familyType: FamilyType, baselineQuote: BaselineQuoteData) => {
  if (!baselineQuote || !baselineQuote.total_cost) {
    return {
      totalPrice: 0,
      breakdown: {
        hotelCost: 0,
        otherCosts: 0,
        commission: 0,
        gst: 0,
        discount: 0,
        roomsNeeded: 0,
        extraAdults: 0,
        childrenCharged: 0,
        infantsFree: 0
      }
    };
  }

  // Calculate room requirements
  const roomReq = calculateRoomRequirements(familyType);

  // Calculate hotel costs
  const familyHotelCosts = baselineQuote.hotelRows.map(row => {
    const adjustedRow: HotelRow = { ...row, noOfRooms: roomReq.roomsNeeded };
    return calculateHotelStayPrice(adjustedRow, roomReq.extraAdults, roomReq.childrenCharged, roomReq.infantsFree);
  });

  const totalHotelCost = familyHotelCosts.reduce((sum, cost) => sum + cost.finalPrice, 0);

  // Calculate other costs
  const baselineAdults = (baselineQuote.no_of_persons || 0) + (baselineQuote.extra_adults || 0);
  const familyAdults = familyType.adults || familyType.no_of_adults || 0;
  const adultRatio = baselineAdults > 0 ? familyAdults / baselineAdults : 1;

  // Apply vehicle multiplier to transportation costs for detailed calculation too
  const vehicleMultiplier = getVehicleTypeMultiplier(familyType);
  
  const scaledTransportationCosts = (
    (baselineQuote.costs.basicCosts.transportation || 0) * adultRatio * vehicleMultiplier +
    (baselineQuote.costs.basicCosts.cabSightseeing || 0) * adultRatio * vehicleMultiplier
  );
  
  const scaledOtherBasicCosts = (
    (baselineQuote.costs.basicCosts.meals || 0) * adultRatio +
    (baselineQuote.costs.basicCosts.trainCost || 0) * adultRatio +
    (baselineQuote.costs.basicCosts.ferryCost || 0) * adultRatio +
    (baselineQuote.costs.basicCosts.parkingToll || 0) * adultRatio
  );
  
  const scaledBasicCosts = scaledTransportationCosts + scaledOtherBasicCosts;
  const scaledAddOnCosts = Object.values(baselineQuote.costs.addOnCosts).reduce((sum, cost) => sum + (cost * adultRatio), 0);
  const scaledOptionalCosts = Object.values(baselineQuote.costs.optionalCosts).reduce((sum, cost) => sum + (cost * adultRatio), 0);

  const otherCostsTotal = scaledBasicCosts + scaledAddOnCosts + scaledOptionalCosts;

  // Calculate totals
  const initialSubtotal = totalHotelCost + otherCostsTotal;
  const discountAmount = baselineQuote.discountAmount || 0;
  const subtotal = initialSubtotal - discountAmount;

  const commissionRate = baselineQuote.commission_rate || 5;
  const commissionAmount = subtotal * (commissionRate / 100);
  const totalWithCommission = subtotal + commissionAmount;

  const gstRate = baselineQuote.gstRate || 0.05;
  const gst = totalWithCommission * gstRate;
  const grandTotal = totalWithCommission + gst;

  return {
    totalPrice: Math.round(Math.max(0, grandTotal)),
    breakdown: {
      hotelCost: Math.round(totalHotelCost),
      otherCosts: Math.round(otherCostsTotal),
      commission: Math.round(commissionAmount),
      gst: Math.round(gst),
      discount: Math.round(discountAmount),
      roomsNeeded: roomReq.roomsNeeded,
      extraAdults: roomReq.extraAdults,
      childrenCharged: roomReq.childrenCharged,
      infantsFree: roomReq.infantsFree
    }
  };
};

// Vehicle type pricing multipliers based on family requirements
const getVehicleTypeMultiplier = (familyType: FamilyType): number => {
  const cabType = (familyType.cab_type || familyType.cabType || '').toLowerCase();
  const capacity = familyType.cab_capacity || 0;
  const totalPax = (familyType.no_of_adults || familyType.adults || 0) + 
                   (familyType.no_of_children || familyType.children || 0) + 
                   (familyType.no_of_infants || familyType.infants || 0);

  // Determine multiplier based on cab type and capacity
  if (cabType.includes('sedan') || cabType.includes('dzire') || cabType.includes('etios')) {
    return 1.0; // Base price for sedan (4 seater)
  } else if (cabType.includes('innova') || cabType.includes('crysta') || cabType.includes('ertiga')) {
    return 1.20; // 20% increase for Innova type (7 seater)
  } else if (cabType.includes('suv') || cabType.includes('xuv') || cabType.includes('scorpio')) {
    return 1.25; // 25% increase for SUV (7-8 seater)
  } else if (cabType.includes('tempo') || cabType.includes('traveller')) {
    if (capacity <= 12) {
      return 1.40; // 40% increase for small tempo traveller (9-12 seater)
    } else if (capacity <= 17) {
      return 1.60; // 60% increase for medium tempo traveller (13-17 seater)
    } else {
      return 1.80; // 80% increase for large tempo traveller (18+ seater)
    }
  } else if (cabType.includes('mini bus') || cabType.includes('bus')) {
    if (capacity <= 25) {
      return 2.0; // 100% increase for mini bus (18-25 seater)
    } else if (capacity <= 35) {
      return 2.5; // 150% increase for medium bus (26-35 seater)
    } else {
      return 3.0; // 200% increase for large bus (35+ seater)
    }
  } else if (cabType.includes('luxury') || cabType.includes('premium')) {
    return 1.5; // 50% increase for luxury vehicles
  } else {
    // Auto-determine based on total passengers if cab type is unclear
    if (totalPax <= 4) return 1.0;      // Sedan
    else if (totalPax <= 7) return 1.20;  // Innova
    else if (totalPax <= 12) return 1.40; // Small Tempo
    else if (totalPax <= 17) return 1.60; // Medium Tempo
    else if (totalPax <= 25) return 2.0;  // Mini Bus
    else return 2.5; // Large Bus
  }
}; 