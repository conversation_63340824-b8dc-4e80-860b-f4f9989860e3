/* Import CSS Variables */
@import url('styleguide.css');

/* Reset and Base Styles - Override globals.css reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  margin: 0 !important;
  padding: 0 !important;
  height: 100% !important;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  line-height: 1.6 !important;
  color: #080809 !important;
  background-color: #ffffff !important;
}

/* Override reset styles */
h1, h2, h3, h4, h5, h6 {
  font-weight: inherit !important;
  font-size: inherit !important;
  margin: 0 !important;
  padding: 0 !important;
}

p {
  margin: 0 !important;
  padding: 0 !important;
}

button {
  font-family: inherit !important;
  cursor: pointer !important;
}

input {
  font-family: inherit !important;
}

.family-emi-website {
  min-height: 100vh !important;
  background: linear-gradient(135deg, #8B5CF6 0%, #3B82F6 100%) !important;
  position: relative !important;
  overflow-x: hidden !important;
  width: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Navigation */
.navigation {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  z-index: 1000 !important;
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(10px) !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
  width: 100% !important;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  font-size: 1.5rem;
  font-weight: 800;
  letter-spacing: 1px;
}

.logo-trip {
  color: var(--green, #32d69f);
}

.logo-xplo {
  color: var(--x-1st, #181e4b);
}

.nav-links {
  display: flex;
  gap: 2rem;
}

.nav-links a {
  text-decoration: none;
  color: var(--black, #080809);
  font-weight: 500;
  transition: color 0.3s ease;
}

.nav-links a:hover {
  color: var(--x-2nd, #6246e5);
}

/* Hero Section */
.hero-section {
  min-height: 100vh !important;
  display: flex !important;
  flex-direction: column !important;
  justify-content: center !important;
  align-items: center !important;
  padding: 8rem 2rem 4rem !important;
  position: relative !important;
  overflow: hidden !important;
  background: linear-gradient(135deg, #8B5CF6 0%, #3B82F6 100%) !important;
  width: 100% !important;
}

.hero-content {
  max-width: 800px;
  width: 100%;
  text-align: center;
  z-index: 10;
}

.hero-text {
  margin-bottom: 3rem;
}

.hero-title {
  font-size: 3.5rem !important;
  font-weight: 700 !important;
  color: white !important;
  margin-bottom: 1rem !important;
  line-height: 1.2 !important;
  text-align: center !important;
  font-family: 'Inter', sans-serif !important;
}

.hero-subtitle {
  font-size: 1.25rem !important;
  color: rgba(255, 255, 255, 0.9) !important;
  margin-bottom: 2rem !important;
  text-align: center !important;
  font-family: 'Inter', sans-serif !important;
}

/* Search Form */
.search-form-container {
  background: white !important;
  border-radius: 20px !important;
  padding: 2rem !important;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1) !important;
  margin-bottom: 1rem !important;
  width: 100% !important;
  max-width: 800px !important;
}

.search-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 1.5rem;
}

.input-group {
  position: relative;
}

.input-label {
  display: block;
  font-weight: 600;
  color: var(--text-clr, #5e6282);
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.input-label i {
  margin-right: 0.5rem;
  color: var(--x-2nd, #6246e5);
}

.form-input {
  width: 100%;
  padding: 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: white;
}

.form-input:focus {
  outline: none;
  border-color: var(--x-2nd, #6246e5);
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

.traveler-selector {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  text-align: left;
}

.destination-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 100;
  display: none;
}

.suggestion-item {
  padding: 0.75rem 1rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.suggestion-item:hover {
  background-color: #f3f4f6;
}

.search-btn {
  background: linear-gradient(135deg, #6246e5 0%, #181e4b 100%);
  color: white;
  border: none;
  padding: 1.25rem 2rem;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.search-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(139, 92, 246, 0.3);
}

.family-type-display {
  text-align: center;
  padding: 1rem;
  background: rgba(139, 92, 246, 0.1);
  border-radius: 12px;
  margin-top: 1rem;
}

.family-type-label {
  font-weight: 600;
  color: var(--text-clr, #5e6282);
  margin-right: 0.5rem;
}

.family-type-name {
  font-weight: 700;
  color: var(--x-2nd, #6246e5);
}

/* Hero Background */
.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.hero-plane {
  position: absolute;
  width: 60px;
  height: auto;
  opacity: 0.3;
  animation: float 6s ease-in-out infinite;
}

.hero-plane-1 {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.hero-plane-2 {
  top: 60%;
  right: 15%;
  animation-delay: 3s;
}

.hero-decoration {
  position: absolute;
  width: 400px;
  height: 400px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  top: 10%;
  right: -200px;
  filter: blur(100px);
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

/* Hide all old travel-agency styles */
.travel-agency * {
  display: none !important;
}

/* Results Section */
.results-section {
  background: white;
  padding: 4rem 2rem;
  min-height: 100vh;
}

.results-container {
  max-width: 1200px;
  margin: 0 auto;
}

.results-header {
  text-align: center;
  margin-bottom: 3rem;
}

.results-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--black, #080809);
  line-height: 1.3;
}

.family-type-highlight {
  color: var(--x-2nd, #6246e5);
}

.destination-highlight {
  color: var(--green, #32d69f);
}

.date-highlight {
  color: var(--x-4, #f15a2b);
}

/* Package Grid */
.package-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.package-card {
  background: white;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 1px solid #f1f5f9;
}

.package-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.package-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.package-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.package-card:hover .package-image img {
  transform: scale(1.05);
}

.duration-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
}

.offer-badge {
  position: absolute;
  top: 1rem;
  left: 1rem;
  background: var(--green, #32d69f);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.offer-badge.early-bird {
  background: var(--yellow, #f2c94c);
  color: var(--black, #080809);
}

.offer-badge.best-value {
  background: var(--x-4, #f15a2b);
}

.package-content {
  padding: 1.5rem;
}

.package-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--black, #080809);
  margin-bottom: 1rem;
}

.package-inclusions {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.inclusion-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: var(--text-clr, #5e6282);
}

.inclusion-item i {
  color: var(--x-2nd, #6246e5);
  font-size: 1rem;
}

.emi-highlight {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%);
  padding: 1.5rem;
  border-radius: 12px;
  margin-bottom: 1.5rem;
  text-align: center;
}

.emi-amount {
  font-size: 2rem;
  font-weight: 800;
  color: var(--x-2nd, #6246e5);
  margin-bottom: 0.5rem;
}

.emi-period {
  font-size: 1rem;
  font-weight: 500;
  color: var(--text-clr, #5e6282);
}

.emi-details {
  font-size: 1rem;
  color: var(--text-clr, #5e6282);
  margin-bottom: 0.5rem;
}

.total-amount {
  font-size: 0.9rem;
  color: var(--text-2, #84829a);
}

.view-details-btn {
  width: 100%;
  padding: 1rem;
  background: transparent;
  border: 2px solid var(--x-2nd, #6246e5);
  color: var(--x-2nd, #6246e5);
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.view-details-btn:hover {
  background: var(--x-2nd, #6246e5);
  color: white;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: none;
  justify-content: center;
  align-items: center;
  z-index: 2000;
  padding: 2rem;
}

.modal-content {
  background: white;
  border-radius: 20px;
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem 2rem 1rem;
  border-bottom: 1px solid #f1f5f9;
}

.modal-header h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--black, #080809);
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--text-clr, #5e6282);
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.modal-close:hover {
  background: #f1f5f9;
  color: var(--black, #080809);
}

.modal-body {
  padding: 2rem;
}

.modal-footer {
  padding: 1rem 2rem 2rem;
  border-top: 1px solid #f1f5f9;
}

/* Traveler Modal */
.traveler-modal {
  max-width: 500px;
}

.traveler-counters {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.counter-group {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
}

.counter-info {
  display: flex;
  flex-direction: column;
}

.counter-label {
  font-weight: 600;
  color: var(--black, #080809);
  font-size: 1rem;
}

.counter-sublabel {
  font-size: 0.85rem;
  color: var(--text-clr, #5e6282);
}

.counter-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.counter-btn {
  width: 40px;
  height: 40px;
  border: 2px solid var(--x-2nd, #6246e5);
  background: white;
  color: var(--x-2nd, #6246e5);
  border-radius: 50%;
  font-size: 1.2rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.counter-btn:hover {
  background: var(--x-2nd, #6246e5);
  color: white;
}

.counter-value {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--black, #080809);
  min-width: 30px;
  text-align: center;
}

.detected-family-type {
  background: rgba(139, 92, 246, 0.1);
  padding: 1rem;
  border-radius: 12px;
  text-align: center;
}

.family-type-value {
  font-weight: 700;
  color: var(--x-2nd, #6246e5);
}

.apply-btn {
  width: 100%;
  padding: 1rem;
  background: linear-gradient(135deg, #6246e5 0%, #181e4b 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.apply-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(139, 92, 246, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-container {
    padding: 1rem;
  }

  .nav-links {
    display: none;
  }

  .hero-title {
    font-size: 2.5rem !important;
  }

  .hero-subtitle {
    font-size: 1.1rem !important;
  }

  .search-form-container {
    padding: 1.5rem !important;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .package-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .results-title {
    font-size: 1.5rem;
  }

  .modal-overlay {
    padding: 1rem;
  }

  .modal-content {
    max-height: 95vh;
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 1.5rem;
  }

  .hero-plane {
    display: none;
  }
}

/* Features Section */
.features-section {
  background: #f8fafc;
  padding: 4rem 2rem;
}

.features-container {
  max-width: 1200px;
  margin: 0 auto;
}

.features-header {
  text-align: center;
  margin-bottom: 3rem;
}

.features-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--black, #080809);
  margin-bottom: 1rem;
}

.features-subtitle {
  font-size: 1.2rem;
  color: var(--text-clr, #5e6282);
  max-width: 600px;
  margin: 0 auto;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
}

.feature-card {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 1px solid #f1f5f9;
}

.feature-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.feature-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 1.5rem;
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.feature-icon img {
  width: 40px;
  height: 40px;
  filter: brightness(0) saturate(100%) invert(45%) sepia(84%) saturate(2482%) hue-rotate(244deg) brightness(98%) contrast(95%);
}

.feature-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--black, #080809);
  margin-bottom: 1rem;
}

.feature-description {
  color: var(--text-clr, #5e6282);
  line-height: 1.6;
}

/* Package Modal Tabs */
.package-modal {
  max-width: 800px;
}

.image-gallery {
  margin-bottom: 2rem;
}

.main-image {
  width: 100%;
  height: 300px;
  border-radius: 12px;
  overflow: hidden;
}

.main-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.package-tabs {
  display: flex;
  border-bottom: 2px solid #f1f5f9;
  margin-bottom: 2rem;
}

.tab-btn {
  flex: 1;
  padding: 1rem;
  background: none;
  border: none;
  font-weight: 600;
  color: var(--text-clr, #5e6282);
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 3px solid transparent;
}

.tab-btn.active {
  color: var(--x-2nd, #6246e5);
  border-bottom-color: var(--x-2nd, #6246e5);
}

.tab-content {
  min-height: 300px;
}

.tab-pane {
  display: none;
}

.tab-pane.active {
  display: block;
}

.package-overview {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.overview-item {
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
}

.overview-item strong {
  color: var(--black, #080809);
  margin-right: 0.5rem;
}

.overview-item ul {
  margin-top: 0.5rem;
  margin-left: 1rem;
}

.overview-item li {
  margin-bottom: 0.25rem;
  color: var(--text-clr, #5e6282);
}

.itinerary-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.day-item {
  padding: 1.5rem;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
}

.day-item h4 {
  color: var(--x-2nd, #6246e5);
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
}

.day-item p {
  color: var(--text-clr, #5e6282);
  line-height: 1.6;
}

.emi-plans {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.emi-plan {
  border: 2px solid #e5e7eb;
  border-radius: 16px;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
}

.emi-plan.best-value {
  border-color: var(--green, #32d69f);
  background: rgba(50, 214, 159, 0.05);
}

.emi-plan:hover {
  border-color: var(--x-2nd, #6246e5);
  transform: translateY(-4px);
}

.plan-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.plan-header h4 {
  font-size: 1.1rem;
  font-weight: 700;
  color: var(--black, #080809);
}

.plan-label {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-weight: 600;
}

.plan-label.quick-pay {
  background: var(--yellow, #f2c94c);
  color: var(--black, #080809);
}

.plan-label:not(.quick-pay):not(.low-monthly) {
  background: var(--green, #32d69f);
  color: white;
}

.plan-label.low-monthly {
  background: var(--x-4, #f15a2b);
  color: white;
}

.plan-amount {
  font-size: 1.5rem;
  font-weight: 800;
  color: var(--x-2nd, #6246e5);
  margin-bottom: 1rem;
}

.plan-details {
  margin-bottom: 1.5rem;
  font-size: 0.9rem;
  color: var(--text-clr, #5e6282);
}

.plan-details div {
  margin-bottom: 0.25rem;
}

.select-plan-btn {
  width: 100%;
  padding: 0.75rem;
  background: var(--x-2nd, #6246e5);
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.select-plan-btn:hover {
  background: var(--x-1st, #181e4b);
  transform: translateY(-2px);
}

/* Show only our new family-emi-website styles */
.family-emi-website,
.family-emi-website * {
  display: initial !important;
}
