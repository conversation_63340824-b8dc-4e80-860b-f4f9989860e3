// Enhanced Family Type Price Calculator following exact Quote Generation Workflow
import { getQuoteClient, getCrmClient } from '../../lib/supabaseManager';

// =============================================================================
// INTERFACES - Exact specifications from user requirements
// =============================================================================

export interface FamilyTypeDB {
  family_id: string;
  family_type: string;
  no_of_adults: number;
  no_of_infants: number;     // Below 2 yrs - Free
  no_of_child: number;       // Below 5 yrs - Free  
  no_of_children: number;    // 6-12 yrs - Charged
  family_count: number;      // Total family members
  cab_type: string;          // Required vehicle type
  cab_capacity: number;      // Vehicle capacity
  rooms_need: number;        // Rooms required
}

export interface BaselineQuote {
  id: string;
  package_name: string;
  customer_name: string;
  destination: string;
  total_cost: number;
  no_of_persons: number;
  extra_adults: number;
  children: number;
  infants: number;
  nights?: number;
  commission_rate?: number;
  discount_amount?: number;
  gst_rate?: number;
}

export interface QuoteMappingData {
  id: string;
  quote_id: string;
  hotel_mappings: HotelMapping[];
  vehicle_mappings: VehicleMapping[];
  additional_costs: AdditionalCosts;
}

export interface HotelMapping {
  hotel_name: string;
  room_type: string;
  price_per_night: number;
  max_occupancy: number;
  extra_adult_cost: number;  // Per night
  children_cost: number;     // Per night (6-12 years)
  infant_cost: number;       // Per night (usually 0)
  gst_rate: number;
  meal_plan: string;
}

export interface VehicleMapping {
  vehicle_type: string;
  pricing_type: 'actual_cost' | 'multiplier';
  base_cost: number;
  cost_multiplier: number;
  max_capacity: number;
  is_active: boolean;
}

export interface AdditionalCosts {
  meal_cost_per_person: number;
  transportation_base: number;
  cab_sightseeing_base: number;
  train_cost: number;
  ferry_cost: number;
  parking_toll: number;
  activity_cost_per_person: number;
  marketing_cost: number;
  guide_cost_per_day: number;
}

export interface RoomCalculationResult {
  roomsNeeded: number;
  roomType: string;
  extraAdults: number;
  childrenCharged: number;    // Children 6-12 years
  childrenFree: number;       // Children ≤5 years
  infantsFree: number;        // Infants ≤2 years
  totalOccupancy: number;
  cabRequired: string;
  cabCapacity: number;
  calculation_notes: string[];
}

export interface HotelCostBreakdown {
  baseRoomCost: number;
  extraAdultCost: number;
  childrenCost: number;
  infantCost: number;
  subtotal: number;
  gst: number;
  totalHotelCost: number;
  nights: number;
}

export interface FamilyTypePriceResult {
  familyType: FamilyTypeDB;
  roomCalculation: RoomCalculationResult;
  hotelCosts: HotelCostBreakdown;
  vehicleCosts: {
    vehicleType: string;
    baseCost: number;
    multiplier: number;
    totalCost: number;
  };
  additionalCosts: {
    meals: number;
    transportation: number;
    activities: number;
    other: number;
    total: number;
  };
  finalCalculation: {
    subtotal: number;
    discount: number;
    afterDiscount: number;
    commission: number;
    afterCommission: number;
    gst: number;
    grandTotal: number;
  };
}

// =============================================================================
// PHASE 1: Basic Family Information Setup
// =============================================================================

export const fetchFamilyTypesFromDatabase = async (): Promise<FamilyTypeDB[]> => {
  try {
    console.log('🔄 Phase 1: Fetching family types from TripXplo Quote DB...');
    
    const supabase = getCrmClient(); // Family types are in CRM DB
    if (!supabase) {
      console.error('❌ CRM Supabase client not available');
      return [];
    }

    const { data, error } = await supabase
      .from('family_type')
      .select(`
        family_id,
        family_type,
        no_of_adults,
        no_of_infants,
        no_of_child,
        no_of_children,
        family_count,
        cab_type,
        cab_capacity,
        rooms_need
      `)
      .order('family_id');

    if (error) {
      console.error('❌ Error fetching family types:', error);
      return [];
    }

    console.log(`✅ Phase 1 Complete: Fetched ${data?.length || 0} family types`);
    console.log('📊 Family types preview:', data?.slice(0, 3).map(ft => ({
      id: ft.family_id,
      type: ft.family_type,
      adults: ft.no_of_adults,
      children: ft.no_of_children,
      infants: ft.no_of_infants,
      cab: ft.cab_type
    })));

    return data || [];
  } catch (error) {
    console.error('❌ Exception in Phase 1:', error);
    return [];
  }
};

// =============================================================================
// PHASE 2: Setup Family, Room Count & Cab Type
// =============================================================================

export const calculateFamilyRoomRequirements = (familyType: FamilyTypeDB): RoomCalculationResult => {
  console.log(`\n🏨 Phase 2: Room & Cab calculation for ${familyType.family_type}`);
  
  const adults = familyType.no_of_adults;
  const childrenFree = familyType.no_of_child;      // Below 5 yrs - Free
  const childrenCharged = familyType.no_of_children; // 6-12 yrs - Charged  
  const infantsFree = familyType.no_of_infants;     // Below 2 yrs - Free
  const totalFamily = familyType.family_count;
  const roomsFromDB = familyType.rooms_need;
  const cabFromDB = familyType.cab_type;
  const cabCapacityFromDB = familyType.cab_capacity;

  const notes: string[] = [];

  console.log('👥 Family Composition:', {
    adults,
    childrenFree: `${childrenFree} (≤5 yrs)`,
    childrenCharged: `${childrenCharged} (6-12 yrs)`,
    infantsFree: `${infantsFree} (≤2 yrs)`,
    totalFamily,
    roomsFromDB,
    cabFromDB,
    cabCapacityFromDB
  });

  // Hotel Room Calculation Steps (following exact specifications):
  // 1 Hotel Room is for 2 Adults Occupancy & 2 Child below 5 yrs - Globally followed
  // 1 Extra Adult will be Allowed in Each Room
  // Eg: Deluxe Room (2 Adults + 1 Extra Adult), Family Room (4 Adults + 1 Extra Adults)
  // Infant & child <= 5 Free of Cost (no_of_infants, no_of_child)
  // Children > 6 & less than 12 Children Cost will be Calculated (no_of_children)

  let roomsNeeded = roomsFromDB || 1;
  let roomType = 'Standard';
  let extraAdults = 0;

  // Calculate based on adults (paying occupants)
  if (adults <= 2) {
    roomsNeeded = 1;
    roomType = 'Standard/Deluxe';
    extraAdults = 0;
    notes.push('Standard room for 2 adults');
  } else if (adults === 3) {
    roomsNeeded = 1;
    roomType = 'Standard/Deluxe';
    extraAdults = 1; // 1 extra adult in standard room
    notes.push('Standard room with 1 extra adult');
  } else if (adults === 4) {
    roomsNeeded = 1;
    roomType = 'Family Room';
    extraAdults = 0; // Family room accommodates 4 adults
    notes.push('Family room for 4 adults');
  } else if (adults === 5) {
    roomsNeeded = 1;
    roomType = 'Family Room';
    extraAdults = 1; // Family room + 1 extra adult
    notes.push('Family room with 1 extra adult');
  } else {
    // For more than 5 adults, need multiple rooms
    roomsNeeded = Math.ceil(adults / 3); // Max 3 adults per room (2 + 1 extra)
    roomType = 'Multiple Rooms';
    extraAdults = adults - (roomsNeeded * 2); // Extra adults beyond 2 per room
    notes.push(`${roomsNeeded} rooms needed for ${adults} adults`);
  }

  // Use database value if specified and reasonable
  if (roomsFromDB > 0 && roomsFromDB !== roomsNeeded) {
    notes.push(`Database specifies ${roomsFromDB} rooms (calculated: ${roomsNeeded})`);
    roomsNeeded = roomsFromDB;
    
    // Recalculate extra adults based on DB rooms
    const standardOccupancy = roomsNeeded * 2;
    extraAdults = Math.max(0, adults - standardOccupancy);
  }

  // Vehicle Requirements
  let cabRequired = cabFromDB || 'Sedan - 4 Seater';
  let cabCapacity = cabCapacityFromDB || 4;

  // Validate cab capacity vs family count
  if (totalFamily > cabCapacity) {
    notes.push(`⚠️ Cab undersized: ${totalFamily} people, ${cabCapacity} capacity`);
  } else {
    notes.push(`✅ Cab suitable: ${totalFamily} people, ${cabCapacity} capacity`);
  }

  const result: RoomCalculationResult = {
    roomsNeeded,
    roomType,
    extraAdults: Math.max(0, extraAdults),
    childrenCharged,
    childrenFree,
    infantsFree,
    totalOccupancy: adults + childrenCharged + childrenFree + infantsFree,
    cabRequired,
    cabCapacity,
    calculation_notes: notes
  };

  console.log('🏨 Room Calculation Result:', result);
  console.log('📝 Calculation Notes:', notes);

  return result;
};

// =============================================================================
// PHASE 3: Hotel Configuration
// =============================================================================

export const calculateHotelCosts = (
  familyType: FamilyTypeDB,
  roomReq: RoomCalculationResult,
  baselineQuote: BaselineQuote,
  quoteMappingData?: QuoteMappingData
): HotelCostBreakdown => {
  console.log(`\n💰 Phase 3: Hotel cost calculation for ${familyType.family_type}`);
  
  const nights = baselineQuote.nights || 3; // Default 3 nights
  
  // Room Cost = No of Nights * No of Rooms + Extra Adult + Children Cost + GST
  
  let baseRoomCost = 0;
  let extraAdultCost = 0;
  let childrenCost = 0;
  let infantCost = 0;
  let gst = 0;

  if (quoteMappingData && quoteMappingData.hotel_mappings.length > 0) {
    console.log('✅ Using Quote Mapping hotel data');
    
    // Use actual hotel mapping data with fallbacks
    const hotelMapping = quoteMappingData.hotel_mappings[0]; // Use first hotel
    
    // Add fallbacks for missing data
    const pricePerNight = hotelMapping.price_per_night || (baselineQuote.total_cost * 0.4 / (3 * roomReq.roomsNeeded));
    const extraAdultRate = hotelMapping.extra_adult_cost || 1000;
    const childrenRate = hotelMapping.children_cost || 700;
    const infantRate = hotelMapping.infant_cost || 0;
    const gstRate = hotelMapping.gst_rate || 0.12;
    
    baseRoomCost = pricePerNight * roomReq.roomsNeeded * nights;
    extraAdultCost = extraAdultRate * roomReq.extraAdults * nights;
    childrenCost = childrenRate * roomReq.childrenCharged * nights;
    infantCost = infantRate * roomReq.infantsFree * nights;
    
    const subtotal = baseRoomCost + extraAdultCost + childrenCost + infantCost;
    gst = subtotal * gstRate;
    
    console.log('🏨 Hotel mapping values:', {
      pricePerNight: pricePerNight.toFixed(0),
      extraAdultRate,
      childrenRate,
      infantRate,
      gstRate
    });
    
  } else {
    console.log('⚠️ No hotel mapping, estimating from baseline quote');
    
    // Estimate from baseline quote (40% of total cost is typically accommodation)
    const baselineHotelCost = baselineQuote.total_cost * 0.4;
    const baselineAdults = baselineQuote.no_of_persons + baselineQuote.extra_adults;
    const baselineRooms = Math.ceil(baselineAdults / 2);
    
    // Scale based on room requirements
    const roomScalingFactor = roomReq.roomsNeeded / Math.max(baselineRooms, 1);
    baseRoomCost = baselineHotelCost * roomScalingFactor;
    
    // Estimate extra costs
    extraAdultCost = roomReq.extraAdults * 1000 * nights; // ₹1000 per extra adult per night
    childrenCost = roomReq.childrenCharged * 700 * nights; // ₹700 per child per night
    infantCost = roomReq.infantsFree * 0; // Infants free
    
    const subtotal = baseRoomCost + extraAdultCost + childrenCost + infantCost;
    gst = subtotal * 0.12; // 12% GST
  }

  const totalHotelCost = baseRoomCost + extraAdultCost + childrenCost + infantCost + gst;

  const breakdown: HotelCostBreakdown = {
    baseRoomCost: Math.round(baseRoomCost),
    extraAdultCost: Math.round(extraAdultCost),
    childrenCost: Math.round(childrenCost),
    infantCost: Math.round(infantCost),
    subtotal: Math.round(baseRoomCost + extraAdultCost + childrenCost + infantCost),
    gst: Math.round(gst),
    totalHotelCost: Math.round(totalHotelCost),
    nights
  };

  console.log('💰 Hotel Cost Breakdown:', breakdown);
  console.log(`🏨 ${roomReq.roomsNeeded} rooms × ${nights} nights = Base ₹${breakdown.baseRoomCost}`);
  console.log(`👥 ${roomReq.extraAdults} extra adults = ₹${breakdown.extraAdultCost}`);
  console.log(`👶 ${roomReq.childrenCharged} charged children = ₹${breakdown.childrenCost}`);
  console.log(`🍼 ${roomReq.infantsFree} free infants = ₹${breakdown.infantCost}`);

  return breakdown;
};

// =============================================================================
// PHASE 4: Additional Costs & Vehicle Calculation
// =============================================================================

export const calculateVehicleCosts = (
  familyType: FamilyTypeDB,
  baselineQuote: BaselineQuote,
  quoteMappingData?: QuoteMappingData
) => {
  console.log(`\n🚗 Phase 4a: Vehicle cost calculation`);
  
  const cabType = familyType.cab_type.toLowerCase();
  const familyCount = familyType.family_count;
  const cabCapacity = familyType.cab_capacity;

  console.log(`🚗 Required: ${familyType.cab_type} (${cabCapacity} seats) for ${familyCount} people`);

  let vehicleType = familyType.cab_type;
  let multiplier = 1.0;
  let baseCost = baselineQuote.total_cost * 0.25; // 25% of total for vehicle

  // Try to find vehicle mapping
  if (quoteMappingData && quoteMappingData.vehicle_mappings.length > 0) {
    const vehicleMapping = quoteMappingData.vehicle_mappings.find(vm => 
      vm.is_active &&
      vm.max_capacity >= familyCount &&
      vm.vehicle_type.toLowerCase().includes(cabType.split(' ')[0])
    );

    if (vehicleMapping) {
      vehicleType = vehicleMapping.vehicle_type;
      if (vehicleMapping.pricing_type === 'actual_cost') {
        baseCost = vehicleMapping.base_cost;
        multiplier = 1.0;
      } else {
        multiplier = vehicleMapping.cost_multiplier;
      }
      console.log(`✅ Found vehicle mapping: ${vehicleType} (multiplier: ${multiplier})`);
    }
  }

  // Fallback multipliers based on vehicle type and capacity
  if (multiplier === 1.0 && !vehicleType.includes('mapping')) {
    if (cabType.includes('sedan') || cabType.includes('dzire')) {
      multiplier = 1.0;
      vehicleType = 'Sedan/Dzire';
    } else if (cabType.includes('innova') || cabType.includes('crysta')) {
      multiplier = 1.2;
      vehicleType = 'Toyota Innova AC';
    } else if (cabType.includes('suv') || cabType.includes('scorpio')) {
      multiplier = 1.25;
      vehicleType = 'SUV/Scorpio';
    } else if (cabType.includes('tempo')) {
      multiplier = 1.4;
      vehicleType = 'Tempo Traveller';
    } else if (cabType.includes('bus')) {
      multiplier = 1.6;
      vehicleType = 'Mini Bus';
    }
    console.log(`⚠️ Using fallback: ${vehicleType} (multiplier: ${multiplier})`);
  }

  const totalCost = baseCost * multiplier;

  const result = {
    vehicleType,
    baseCost: Math.round(baseCost),
    multiplier,
    totalCost: Math.round(totalCost)
  };

  console.log('🚗 Vehicle Costs:', result);
  return result;
};

export const calculateAdditionalCosts = (
  familyType: FamilyTypeDB,
  baselineQuote: BaselineQuote,
  quoteMappingData?: QuoteMappingData
) => {
  console.log(`\n🎯 Phase 4b: Additional costs calculation`);
  
  const familyCount = familyType.family_count;
  const nights = baselineQuote.nights || 3;

  let additionalCosts = {
    meals: 0,
    transportation: 0,
    activities: 0,
    other: 0,
    total: 0
  };

  if (quoteMappingData && quoteMappingData.additional_costs) {
    const addCosts = quoteMappingData.additional_costs;
    
    // Add fallbacks for missing additional cost data
    const mealCostPerPerson = addCosts.meal_cost_per_person || (baselineQuote.total_cost * 0.08 / familyCount);
    const transportationBase = addCosts.transportation_base || (baselineQuote.total_cost * 0.15);
    const cabSightseeingBase = addCosts.cab_sightseeing_base || (baselineQuote.total_cost * 0.10);
    const activityCostPerPerson = addCosts.activity_cost_per_person || (baselineQuote.total_cost * 0.05 / familyCount);
    const ferryCost = addCosts.ferry_cost || (baselineQuote.total_cost * 0.03);
    const trainCost = addCosts.train_cost || (baselineQuote.total_cost * 0.05);
    const parkingToll = addCosts.parking_toll || (baselineQuote.total_cost * 0.02);
    const guideCostPerDay = addCosts.guide_cost_per_day || (baselineQuote.total_cost * 0.04 / 3);
    
    // Basic Costs: Meals, transportation, cab sightseeing, train, ferry, parking/toll
    additionalCosts.meals = mealCostPerPerson * familyCount * nights;
    additionalCosts.transportation = transportationBase + cabSightseeingBase;
    
    // Add-on Costs: Activities, marketing, additional services  
    additionalCosts.activities = activityCostPerPerson * familyCount;
    
    // Optional Costs: Guide wages, ferry, train, parking
    additionalCosts.other = ferryCost + trainCost + parkingToll + (guideCostPerDay * nights);
    
    console.log('🎯 Additional costs values:', {
      mealCostPerPerson: mealCostPerPerson.toFixed(0),
      transportationBase: transportationBase.toFixed(0),
      activityCostPerPerson: activityCostPerPerson.toFixed(0),
      ferryCost: ferryCost.toFixed(0)
    });
                           
  } else {
    console.log('⚠️ No additional costs mapping, estimating from baseline');
    
    // Estimate additional costs as percentage of baseline
    const totalAdditional = baselineQuote.total_cost * 0.35; // 35% for additional costs
    
    additionalCosts.meals = totalAdditional * 0.4; // 40% for meals
    additionalCosts.transportation = totalAdditional * 0.3; // 30% for transportation
    additionalCosts.activities = totalAdditional * 0.2; // 20% for activities
    additionalCosts.other = totalAdditional * 0.1; // 10% for other costs
  }

  additionalCosts.total = additionalCosts.meals + additionalCosts.transportation + 
                         additionalCosts.activities + additionalCosts.other;

  // Round all values
  additionalCosts.meals = Math.round(additionalCosts.meals);
  additionalCosts.transportation = Math.round(additionalCosts.transportation);
  additionalCosts.activities = Math.round(additionalCosts.activities);
  additionalCosts.other = Math.round(additionalCosts.other);
  additionalCosts.total = Math.round(additionalCosts.total);

  console.log('🎯 Additional Costs Breakdown:', additionalCosts);
  return additionalCosts;
};

// =============================================================================
// PHASE 5: Final Calculations
// =============================================================================

export const calculateFinalTotals = (
  hotelCosts: HotelCostBreakdown,
  vehicleCosts: any,
  additionalCosts: any,
  baselineQuote: BaselineQuote
) => {
  console.log(`\n💰 Phase 5: Final calculations`);

  // Safety checks to prevent NaN
  const safeHotelCost = isNaN(hotelCosts.totalHotelCost) ? 0 : hotelCosts.totalHotelCost;
  const safeVehicleCost = isNaN(vehicleCosts.totalCost) ? 0 : vehicleCosts.totalCost;
  const safeAdditionalCost = isNaN(additionalCosts.total) ? 0 : additionalCosts.total;

  const subtotal = safeHotelCost + safeVehicleCost + safeAdditionalCost;
  
  console.log(`🔍 Safety check - Hotel: ₹${safeHotelCost}, Vehicle: ₹${safeVehicleCost}, Additional: ₹${safeAdditionalCost}`);
  
  const discount = baselineQuote.discount_amount || 0;
  const afterDiscount = Math.max(0, subtotal - discount);
  
  const commissionRate = baselineQuote.commission_rate || 5;
  const commission = afterDiscount * (commissionRate / 100);
  const afterCommission = afterDiscount + commission;
  
  const gstRate = baselineQuote.gst_rate || 0.05;
  const gst = afterCommission * gstRate;
  const grandTotal = afterCommission + gst;

  const finalCalculation = {
    subtotal: Math.round(subtotal),
    discount: Math.round(discount),
    afterDiscount: Math.round(afterDiscount),
    commission: Math.round(commission),
    afterCommission: Math.round(afterCommission),
    gst: Math.round(gst),
    grandTotal: Math.round(grandTotal)
  };

  console.log('💰 Final Calculation:', finalCalculation);
  console.log(`📊 Total Breakdown: Hotel ₹${safeHotelCost} + Vehicle ₹${safeVehicleCost} + Additional ₹${safeAdditionalCost} = ₹${subtotal}`);
  
  return finalCalculation;
};

// =============================================================================
// MAIN FAMILY TYPE PRICE CALCULATION FUNCTION
// =============================================================================

export const calculateFamilyTypePrice = async (
  familyType: FamilyTypeDB,
  baselineQuote: BaselineQuote,
  quoteMappingData?: QuoteMappingData
): Promise<FamilyTypePriceResult> => {
  
  console.log(`\n🎯 === FAMILY TYPE PRICE CALCULATION ===`);
  console.log(`📊 Family: ${familyType.family_type}`);
  console.log(`💰 Baseline: ${baselineQuote.customer_name} - ₹${baselineQuote.total_cost}`);
  console.log(`🏨 Quote Mapping: ${quoteMappingData ? 'Available' : 'Not Available'}`);

  try {
    // Phase 2: Setup Family, Room Count & Cab Type  
    const roomCalculation = calculateFamilyRoomRequirements(familyType);
    
    // Phase 3: Hotel Configuration
    const hotelCosts = calculateHotelCosts(familyType, roomCalculation, baselineQuote, quoteMappingData);
    
    // Phase 4: Additional Costs & Vehicle
    const vehicleCosts = calculateVehicleCosts(familyType, baselineQuote, quoteMappingData);
    const additionalCosts = calculateAdditionalCosts(familyType, baselineQuote, quoteMappingData);
    
    // Phase 5: Final Calculations
    const finalCalculation = calculateFinalTotals(hotelCosts, vehicleCosts, additionalCosts, baselineQuote);

    const result: FamilyTypePriceResult = {
      familyType,
      roomCalculation,
      hotelCosts,
      vehicleCosts,
      additionalCosts,
      finalCalculation
    };

    console.log(`\n🎉 === CALCULATION COMPLETE ===`);
    console.log(`💰 ${familyType.family_type}: ₹${finalCalculation.grandTotal}`);
    console.log(`🏨 Rooms: ${roomCalculation.roomsNeeded} (${roomCalculation.roomType})`);
    console.log(`🚗 Vehicle: ${vehicleCosts.vehicleType}`);
    
    return result;

  } catch (error) {
    console.error(`❌ Error calculating price for ${familyType.family_type}:`, error);
    throw error;
  }
};

// =============================================================================
// CALCULATE ALL FAMILY TYPES
// =============================================================================

export const calculateAllFamilyTypePrices = async (
  baselineQuote: BaselineQuote,
  quoteMappingData?: QuoteMappingData,
  familyTypes?: FamilyTypeDB[]
): Promise<FamilyTypePriceResult[]> => {
  
  console.log(`\n🎯 === CALCULATING ALL FAMILY TYPE PRICES ===`);
  console.log(`📊 Baseline: ${baselineQuote.customer_name} (${baselineQuote.destination}) - ₹${baselineQuote.total_cost}`);

  // Phase 1: Fetch family types if not provided
  let allFamilyTypes = familyTypes;
  if (!allFamilyTypes || allFamilyTypes.length === 0) {
    allFamilyTypes = await fetchFamilyTypesFromDatabase();
  }

  if (allFamilyTypes.length === 0) {
    console.error('❌ No family types found');
    return [];
  }

  console.log(`✅ Processing ${allFamilyTypes.length} family types`);

  const results: FamilyTypePriceResult[] = [];

  // Calculate price for each family type
  for (const familyType of allFamilyTypes) {
    try {
      const result = await calculateFamilyTypePrice(familyType, baselineQuote, quoteMappingData);
      results.push(result);
      
      // Brief summary for each family type
      console.log(`✅ ${familyType.family_type}: ₹${result.finalCalculation.grandTotal} (${result.roomCalculation.roomsNeeded} rooms, ${result.vehicleCosts.vehicleType})`);
      
    } catch (error) {
      console.error(`❌ Failed to calculate price for ${familyType.family_type}:`, error);
    }
  }

  console.log(`\n🎉 === ALL CALCULATIONS COMPLETE ===`);
  console.log(`✅ Successfully calculated ${results.length}/${allFamilyTypes.length} family type prices`);
  
  // Summary statistics
  if (results.length > 0) {
    const prices = results.map(r => r.finalCalculation.grandTotal);
    const minPrice = Math.min(...prices);
    const maxPrice = Math.max(...prices);
    const avgPrice = prices.reduce((sum, price) => sum + price, 0) / prices.length;
    
    console.log(`📊 Price Range: ₹${minPrice.toLocaleString()} - ₹${maxPrice.toLocaleString()}`);
    console.log(`📊 Average Price: ₹${Math.round(avgPrice).toLocaleString()}`);
  }

  return results;
};

// =============================================================================
// UTILITY FUNCTIONS
// =============================================================================

export const formatPrice = (price: number): string => {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(price);
};

export const createDefaultQuoteMappingData = (baselineQuote: BaselineQuote): QuoteMappingData => {
  console.log('🔧 Creating default Quote Mapping data');

  return {
    id: 'default-mapping',
    quote_id: baselineQuote.id,
    hotel_mappings: [{
      hotel_name: 'Default Hotel',
      room_type: 'Standard',
      price_per_night: Math.round(baselineQuote.total_cost * 0.4 / 3), // 40% of total for 3 nights
      max_occupancy: 4,
      extra_adult_cost: 1000,
      children_cost: 700,
      infant_cost: 0,
      gst_rate: 0.12,
      meal_plan: 'CP'
    }],
    vehicle_mappings: [
      { vehicle_type: 'Sedan/Dzire', pricing_type: 'multiplier', base_cost: 0, cost_multiplier: 1.0, max_capacity: 4, is_active: true },
      { vehicle_type: 'Toyota Innova AC', pricing_type: 'multiplier', base_cost: 0, cost_multiplier: 1.2, max_capacity: 7, is_active: true },
      { vehicle_type: 'SUV/Scorpio', pricing_type: 'multiplier', base_cost: 0, cost_multiplier: 1.25, max_capacity: 8, is_active: true },
      { vehicle_type: 'Tempo Traveller', pricing_type: 'multiplier', base_cost: 0, cost_multiplier: 1.4, max_capacity: 12, is_active: true }
    ],
    additional_costs: {
      meal_cost_per_person: Math.round(baselineQuote.total_cost * 0.08 / Math.max(baselineQuote.no_of_persons, 1)),
      transportation_base: Math.round(baselineQuote.total_cost * 0.15),
      cab_sightseeing_base: Math.round(baselineQuote.total_cost * 0.10),
      train_cost: Math.round(baselineQuote.total_cost * 0.05),
      ferry_cost: Math.round(baselineQuote.total_cost * 0.03),
      parking_toll: Math.round(baselineQuote.total_cost * 0.02),
      activity_cost_per_person: Math.round(baselineQuote.total_cost * 0.05 / Math.max(baselineQuote.no_of_persons, 1)),
      marketing_cost: Math.round(baselineQuote.total_cost * 0.02),
      guide_cost_per_day: Math.round(baselineQuote.total_cost * 0.04 / 3)
    }
  };
}; 