<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="globals.css" />
    <link rel="stylesheet" href="styleguide.css" />
    <link rel="stylesheet" href="style.css" />
  </head>
  <body>
    <div class="travel-agency">
      <footer class="footer">
        <div class="overlap-group">
          <div class="decore"></div>
          <p class="najam-centre">
            Najam Centre, 2nd Floor, 29/108, <br />Old Bridge Road, Near Vishnu Cinemas, <br />Viruthampet, Vellore,
            632006
          </p>
        </div>
        <div class="outbound"><img class="social" src="img/social.png" /></div>
        <div class="overlap">
          <div class="company-desc">
            <div class="group">
              <div class="rectangle"></div>
              <div class="div"></div>
              <div class="rectangle-2"></div>
            </div>
            <p class="make-your-dream">Make your dream vacation affordable <br />with our monthly prepaid EMI plans</p>
          </div>
          <p class="TRIPMILESTONE"><span class="text-wrapper">TRIP</span> <span class="span">MILESTONE</span></p>
        </div>
        <div class="nav-columns">
          <div class="nav-row">
            <div class="text-wrapper-2">Company</div>
            <div class="text-wrapper-3">About</div>
          </div>
          <div class="nav-row-2">
            <div class="text-wrapper-4">Contact</div>
            <div class="text-wrapper-5">Help/FAQ</div>
          </div>
          <div class="nav-row-3">
            <div class="text-wrapper-2">More</div>
            <div class="text-wrapper-3">Cancellation Policy</div>
          </div>
        </div>
        <p class="copyrights">All Rights Reserved @Tripmilestone Tours Pvt Ltd</p>
        <div class="text-wrapper-6">Registered Office:</div>
      </footer>
      <div class="overlap-2">
        <div class="book-a-trip">
          <p class="heading">Book Your Next Trip <br />in 3 Easy Steps</p>
          <div class="image">
            <div class="overlap-3">
              <div class="group-2"></div>
              <div class="overlap-wrapper">
                <div class="overlap-4">
                  <div class="overlap-group-wrapper">
                    <div class="overlap-group-2">
                      <div class="OPTIONS">
                        <div class="LEAF"><img class="img" src="img/leaf-1.svg" /></div>
                        <div class="send"><img class="img" src="img/send-2.svg" /></div>
                        <div class="map-icon"><img class="map" src="img/map-1.svg" /></div>
                      </div>
                      <img class="building" src="img/building-1.png" />
                      <div class="text-wrapper-7">1`4 family going</div>
                      <div class="text-wrapper-8">Family Nest</div>
                      <p class="element-adults-children">2 Adults + 2 Children (&lt; 5 yrs)</p>
                      <img class="rectangle-3" src="img/rectangle-17.png" />
                      <div class="text-wrapper-9">Book your Kashmir Package</div>
                      <img class="line" src="img/line-3.svg" />
                    </div>
                  </div>
                  <img class="heart" src="img/heart-6-1.png" />
                </div>
              </div>
            </div>
          </div>
          <div class="subheading">Easy and Fast</div>
          <div class="values">
            <div class="value">
              <div class="selection-wrapper"><img class="selection" src="img/selection-1.png" /></div>
              <div class="text-wrapper-10">Choose Destination</div>
              <p class="p">
                Choose your ideal travel destination. Then, select a package tier: <br />Silver, Gold, or Platinum, each
                offering varying levels of amenities <br />and experiences.
              </p>
            </div>
            <div class="value-2">
              <div class="taxi-wrapper"><img class="taxi" src="img/taxi-1.png" /></div>
              <p class="pay-the-first">
                Pay the first installment of your EMI to book your entire <br />trip in advance.
              </p>
              <p class="text-wrapper-10">Pay First EMI and Book</p>
            </div>
            <div class="value-3">
              <p class="p">
                Select your package based on family type and preferred <br />travel season (Off, Mid, Peak, Festive).
                Decide on the duration <br />of your EMI payments.
              </p>
              <div class="text-wrapper-10">Customize Your Trip</div>
              <div class="water-sport-wrapper"><img class="water-sport" src="img/water-sport-1.png" /></div>
            </div>
          </div>
        </div>
        <div class="destinations">
          <div class="destination">
            <div class="overlap-5">
              <div class="card">
                <div class="overlap-group-3">
                  <div class="text-wrapper-11">4N Meghalaya</div>
                  <div class="text-wrapper-12">₹4,899</div>
                </div>
              </div>
              <div class="text-wrapper-13">8 EMI</div>
              <div class="text-wrapper-14">Tiny Delight</div>
              <p class="text-wrapper-15">2 Adults + 1 Child (&lt; 5 Yrs)</p>
            </div>
          </div>
          <div class="destination-2">
            <div class="card-wrapper">
              <div class="div-wrapper">
                <div class="overlap-group-3">
                  <div class="text-wrapper-16">2N Ooty</div>
                  <div class="text-wrapper-17">10 EMI</div>
                  <div class="text-wrapper-18">Tiny Delight</div>
                  <p class="element-adults-child">2 Adults + 1 Child (&lt; 5 Yrs)</p>
                  <div class="text-wrapper-19">₹1,399</div>
                </div>
              </div>
            </div>
          </div>
          <div class="destination-3">
            <div class="overlap-6">
              <div class="card">
                <div class="overlap-group-3">
                  <div class="text-wrapper-16">2N Alleppey</div>
                  <div class="text-wrapper-12">₹1,499</div>
                </div>
              </div>
              <div class="text-wrapper-13">12 EMI</div>
              <div class="text-wrapper-14">Family Nest</div>
              <p class="text-wrapper-15">2 Adults + 2 Children (&lt; 5 Yrs)</p>
            </div>
          </div>
          <div class="subheading-2">Top Selling</div>
          <div class="heading-2">Top Destinations</div>
          <div class="heading-3">Family Emi Packages</div>
          <div class="heading-4">Family Types</div>
          <div class="heading-5">Our Packages</div>
          <div class="heading-6">And More....</div>
        </div>
        <div class="services">
          <div class="group-3">
            <div class="text-wrapper-20">+</div>
            <div class="text-wrapper-21">+</div>
            <div class="text-wrapper-22">+</div>
            <div class="text-wrapper-23">+</div>
            <div class="text-wrapper-24">+</div>
            <div class="text-wrapper-25">+</div>
            <div class="text-wrapper-26">+</div>
            <div class="text-wrapper-27">+</div>
            <div class="text-wrapper-28">+</div>
            <div class="text-wrapper-29">+</div>
            <div class="text-wrapper-30">+</div>
            <div class="text-wrapper-31">+</div>
            <div class="text-wrapper-32">+</div>
            <div class="text-wrapper-33">+</div>
            <div class="text-wrapper-34">+</div>
            <div class="text-wrapper-35">+</div>
            <div class="text-wrapper-36">+</div>
            <div class="text-wrapper-37">+</div>
            <div class="text-wrapper-38">+</div>
            <div class="text-wrapper-39">+</div>
            <div class="text-wrapper-40">+</div>
            <div class="text-wrapper-41">+</div>
          </div>
          <div class="text-wrapper-42">CATEGORY</div>
          <div class="overlap-7">
            <div class="overlap-8">
              <div class="why-family-EMI">Why Family Emi Packages</div>
              <div class="category-active">
                <div class="overlap-9">
                  <div class="rectangle-4"></div>
                  <div class="rectangle-5"></div>
                  <p class="swap-plans-days">
                    Swap plans 60 days before the travel date,&nbsp;&nbsp;change destinations or upgrade plans.
                  </p>
                  <div class="text-wrapper-43">Easy Plan Swap</div>
                  <div class="group-4">
                    <div class="overlap-group-4">
                      <div class="rectangle-6"></div>
                      <img class="vector" src="img/vector-6.svg" />
                    </div>
                  </div>
                </div>
              </div>
              <div class="category">
                <div class="overlap-10">
                  <div class="noun-time"></div>
                  <div class="group-5">
                    <div class="overlap-group-5">
                      <div class="rectangle-7"></div>
                      <img class="vector-2" src="img/vector-4.svg" />
                    </div>
                  </div>
                  <p class="text-wrapper-44">Enjoy your family vacation without the stress of peak season crowds.</p>
                  <div class="text-wrapper-45">No Last Minute Rush</div>
                </div>
              </div>
            </div>
            <div class="category-2">
              <div class="group-6">
                <div class="overlap-group-6">
                  <div class="rectangle-8"></div>
                  <img class="vector-3" src="img/vector-2.svg" />
                </div>
              </div>
              <p class="text-wrapper-46">Secure your booking without worrying about last-minute availability issues.</p>
              <div class="text-wrapper-47">Guaranteed Availability</div>
            </div>
          </div>
          <div class="category-3">
            <div class="group-7">
              <div class="overlap-group-7">
                <div class="rectangle-9"></div>
                <img class="vector-4" src="img/vector.svg" />
              </div>
            </div>
            <p class="text-wrapper-48">Earn benefits on each EMI payment and by referring friends or family.</p>
            <div class="text-wrapper-49">Rewards on Booking</div>
          </div>
        </div>
        <div class="hero">
          <div class="overlap-11">
            <img class="top-nav" src="img/top-nav.png" />
            <div class="hero-content">
              <div class="overlap-12">
                <div class="desc">
                  <div class="CTA">
                    
                      <a href="https://zaap.bio/tripmilestone" target="_blank">
                        <img class="CTA-2" src="img/cta-2.png" />
                      </a>
                      <a href="https://zaap.bio/tripmilestone" target="_blank">
                        <img class="CTA-3" src="img/cta.png" />
                      </a>
                    
                    
                  </div>
                  <p class="tagline">TRAVEL YOUR DREAM DESTINATION IN EMI</p>
                  <p class="desc-2">
                    Experience the joy of hassle-free family vacations with Tripmilestone&#39;s Family Prepaid EMI
                    Packages. Discover, relax, and create lasting memories with us!
                  </p>
                  <div class="overlap-group-8">
                    <img class="decore-2" src="img/decore.png" />
                    <div class="heading-7">Family, Prepaid<br />EMI Packages</div>
                  </div>
                </div>
                <div class="image-2">
                  <div class="overlap-13">
                    <img class="plane" src="img/plane.png" />
                    <div class="family-image"></div>
                    <img class="plane-2" src="img/plane.png" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <img class="GOLD-CARD" src="img/gold-card.svg" />
        <img class="SILVER-CARD" src="img/silver-card.svg" />
        <img class="PLATINUM" src="img/platinum.svg" />
        <div class="text-wrapper-50">Baby Bliss</div>
        <div class="text-wrapper-51">Stellar Duo</div>
        <div class="text-wrapper-52">Grand Bliss</div>
        <div class="text-wrapper-53">Tiny Delight</div>
        <div class="text-wrapper-54">Tiny Delight</div>
        <div class="text-wrapper-55">Tiny Delight</div>
        <div class="text-wrapper-56">Family Nest</div>
        <div class="text-wrapper-57">Fantastic Four</div>
        <div class="text-wrapper-58">Family Nest</div>
        <div class="text-wrapper-59">Nebula Nest</div>
        <div class="text-wrapper-60">Parent Plus Two</div>
        <div class="text-wrapper-61">Nebula Nest</div>
        <div class="text-wrapper-62">Orbiting Duo</div>
        <p class="to-view-all-of-our">To View All Of Our Packages</p>
        <div class="text-wrapper-63">Teen Trek</div>
        <div class="text-wrapper-64">Orbiting Duo</div>
        <p class="element-adults-infant">2 Adults + 1 Infant (below 2 Yrs)</p>
        <p class="element-adults-infant-2">2 Adults + 1 Infant (below 2 Yrs)</p>
        <div class="flexcontainer">
          <p class="text">
            <span class="text-wrapper-65">2 Adults + 1 Infant (Below 2 yrs) +<br /></span>
          </p>
          <p class="text">
            <span class="text-wrapper-65">2 Grandparents<br /></span>
          </p>
          <p class="text"><span class="text-wrapper-65"></span></p>
        </div>
        <div class="flexcontainer-2">
          <p class="text">
            <span class="text-wrapper-65">2 Adults + 1 Child (Below 5 yrs) + <br /></span>
          </p>
          <p class="text">
            <span class="text-wrapper-65">2 Grandparents<br /></span>
          </p>
          <p class="text"><span class="text-wrapper-65"></span></p>
        </div>
        <div class="flexcontainer-3">
          <p class="text">
            <span class="text-wrapper-65">2 Adults + 2 Children (Below 5 yrs) + <br /></span>
          </p>
          <p class="text">
            <span class="text-wrapper-65">2 Grandparents<br /></span>
          </p>
          <p class="text"><span class="text-wrapper-65"></span></p>
        </div>
        <div class="flexcontainer-4">
          <p class="text">
            <span class="text-wrapper-65">2 Adults + 1 Child (5 yrs to 11 yrs) + <br /></span>
          </p>
          <p class="text">
            <span class="text-wrapper-65">2 Grandparents<br /></span>
          </p>
          <p class="text"><span class="text-wrapper-65"></span></p>
        </div>
        <div class="flexcontainer-5">
          <p class="text">
            <span class="text-wrapper-65">1 Adult + 1 Child (Below 5 yrs) + <br /></span>
          </p>
          <p class="text">
            <span class="text-wrapper-65">2 Grandparents<br /></span>
          </p>
          <p class="text"><span class="text-wrapper-65"></span></p>
        </div>
        <p class="element-adults-child-2">2 Adults + 1 Child (below 5 Yrs)</p>
        <p class="element-adults-children-2">2 Adults + 2 Children ( 5 Yrs To 11 Yrs )</p>
        <p class="element-adults-children-3">2 Adults + 2 Children (below 5 Yrs)</p>
        <p class="element-adults-children-4">2 Adults + 2 Children (5 Yrs To 11 Yrs)</p>
        <p class="element-adults-children-5">2 Adults + 1 Children (5 Yrs To 11 Yrs)</p>
        <p class="element-adult-children">1 Adult + 2 Children (below 5 Yrs)</p>
        <p class="element-adult-child">1 Adult + 1 Child (below 5 Yrs)</p>
        <p class="element-adults-teenager">2 Adults + 1 Teenager (above 11 Yrs)</p>
        <a href="index.html">
          <p class="TRIPMILESTONE-2"><span class="text-wrapper">TRIP</span> <span class="span">MILESTONE</span></p>
        </a>
        
        <div class="rectangle-10"></div>
      </div>
    </div>
  </body>
</html>
