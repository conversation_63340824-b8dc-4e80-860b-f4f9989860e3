<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>TripXplo Family - Your Family Adventure, Planned & Paid Your Way</title>
    <meta name="description" content="Experience hassle-free family vacations with TripXplo's Family Prepaid EMI Packages. Discover, relax, and create lasting memories with flexible payment options." />

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&family=Volkhov:wght@400;700&display=swap" rel="stylesheet">

    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <link rel="stylesheet" href="styleguide.css" />
    <link rel="stylesheet" href="style-clean.css" />
  </head>
  <body>
    <div class="family-emi-website">
      <!-- Hero Section with Search Form -->
      <section class="hero-section">
        <!-- Navigation -->
        <nav class="navigation">
          <div class="nav-container">
            <div class="logo">
              <span class="logo-trip">TRIP</span><span class="logo-xplo">XPLO</span>
            </div>
            <div class="nav-links">
              <a href="#packages">Packages</a>
              <a href="#about">About</a>
              <a href="#contact">Contact</a>
            </div>
          </div>
        </nav>

        <!-- Hero Content -->
        <div class="hero-content">
          <div class="hero-text">
            <h1 class="hero-title">Your Family Adventure, Planned & Paid Your Way</h1>
            <p class="hero-subtitle">Enter your dream trip details and discover tailored EMI packages instantly</p>
          </div>

          <!-- Search Form -->
          <div class="search-form-container">
            <form class="search-form" id="familySearchForm">
              <div class="form-row">
                <!-- Destination Input -->
                <div class="input-group">
                  <label for="destination" class="input-label">
                    <i class="fas fa-map-marker-alt"></i> Where to?
                  </label>
                  <input
                    type="text"
                    id="destination"
                    name="destination"
                    placeholder="e.g., Kashmir, Goa, Manali"
                    class="form-input"
                    autocomplete="off"
                    value="Kashmir"
                  />
                  <div class="destination-suggestions" id="destinationSuggestions"></div>
                </div>

                <!-- Travel Date Input -->
                <div class="input-group">
                  <label for="travelDate" class="input-label">
                    <i class="fas fa-calendar-alt"></i> When are you travelling?
                  </label>
                  <input
                    type="month"
                    id="travelDate"
                    name="travelDate"
                    class="form-input"
                    min="2024-12"
                    value="2025-03"
                  />
                </div>

                <!-- Traveler Count Input -->
                <div class="input-group">
                  <label for="travelers" class="input-label">
                    <i class="fas fa-users"></i> Who's going?
                  </label>
                  <button type="button" class="form-input traveler-selector" id="travelerSelector">
                    <span id="travelerDisplay">2 Adults</span>
                    <i class="fas fa-chevron-down"></i>
                  </button>
                </div>
              </div>

              <!-- Search Button -->
              <button type="submit" class="search-btn">
                <i class="fas fa-sparkles"></i> Find My Trip
              </button>
            </form>

            <!-- Auto-detected Family Type Display -->
            <div class="family-type-display" id="familyTypeDisplay">
              <span class="family-type-label">Family Type:</span>
              <span class="family-type-name" id="detectedFamilyType">Stellar Duo (2 Adults)</span>
            </div>
          </div>
        </div>

        <!-- Hero Background Elements -->
        <div class="hero-background">
          <img class="hero-plane hero-plane-1" src="img/plane.png" alt="Plane" />
          <img class="hero-plane hero-plane-2" src="img/plane.png" alt="Plane" />
          <div class="hero-decoration"></div>
        </div>
      </section>
        <!-- Dynamic Results Section -->
        <section class="results-section" id="resultsSection" style="display: none;">
          <div class="results-container">
            <!-- Results Header -->
            <div class="results-header">
              <h2 class="results-title" id="resultsTitle">
                Showing packages for <span class="family-type-highlight">Family Nest (2 Adults, 2 Children)</span>
                to <span class="destination-highlight">Kashmir</span> in <span class="date-highlight">October 2025</span>
              </h2>
            </div>

            <!-- Package Cards Grid -->
            <div class="package-grid" id="packageGrid">
              <!-- Package Card 1 -->
              <div class="package-card">
                <div class="package-image">
                  <img src="img/rectangle-14.png" alt="Kashmir Winter Wonderland" />
                  <div class="duration-badge">5N / 6D</div>
                  <div class="offer-badge">
                    <i class="fas fa-gift"></i> 15% OFF
                  </div>
                </div>

                <div class="package-content">
                  <h3 class="package-title">Kashmir Winter Wonderland</h3>

                  <div class="package-inclusions">
                    <div class="inclusion-item">
                      <i class="fas fa-plane"></i>
                      <span>Flights</span>
                    </div>
                    <div class="inclusion-item">
                      <i class="fas fa-hotel"></i>
                      <span>Hotels</span>
                    </div>
                    <div class="inclusion-item">
                      <i class="fas fa-utensils"></i>
                      <span>Meals</span>
                    </div>
                    <div class="inclusion-item">
                      <i class="fas fa-camera"></i>
                      <span>Sightseeing</span>
                    </div>
                  </div>

                  <div class="emi-highlight">
                    <div class="emi-amount">₹4,999<span class="emi-period">/month</span></div>
                    <div class="emi-details">for 8 Prepaid EMIs</div>
                    <div class="total-amount">Total: ₹39,992</div>
                  </div>

                  <button class="view-details-btn" onclick="openPackageModal('kashmir-winter')">
                    View Details
                  </button>
                </div>
              </div>

              <!-- Package Card 2 -->
              <div class="package-card">
                <div class="package-image">
                  <img src="img/rectangle-14-2.png" alt="Goa Beach Paradise" />
                  <div class="duration-badge">4N / 5D</div>
                  <div class="offer-badge early-bird">
                    <i class="fas fa-clock"></i> Early Bird
                  </div>
                </div>

                <div class="package-content">
                  <h3 class="package-title">Goa Beach Paradise</h3>

                  <div class="package-inclusions">
                    <div class="inclusion-item">
                      <i class="fas fa-plane"></i>
                      <span>Flights</span>
                    </div>
                    <div class="inclusion-item">
                      <i class="fas fa-hotel"></i>
                      <span>Hotels</span>
                    </div>
                    <div class="inclusion-item">
                      <i class="fas fa-utensils"></i>
                      <span>Meals</span>
                    </div>
                    <div class="inclusion-item">
                      <i class="fas fa-umbrella-beach"></i>
                      <span>Beach Activities</span>
                    </div>
                  </div>

                  <div class="emi-highlight">
                    <div class="emi-amount">₹3,555<span class="emi-period">/month</span></div>
                    <div class="emi-details">for 9 Prepaid EMIs</div>
                    <div class="total-amount">Total: ₹32,000</div>
                  </div>

                  <button class="view-details-btn" onclick="openPackageModal('goa-beach')">
                    View Details
                  </button>
                </div>
              </div>

              <!-- Package Card 3 -->
              <div class="package-card">
                <div class="package-image">
                  <img src="img/rectangle-14-4.png" alt="Manali Adventure" />
                  <div class="duration-badge">3N / 4D</div>
                  <div class="offer-badge best-value">
                    <i class="fas fa-star"></i> Best Value
                  </div>
                </div>

                <div class="package-content">
                  <h3 class="package-title">Manali Adventure</h3>

                  <div class="package-inclusions">
                    <div class="inclusion-item">
                      <i class="fas fa-plane"></i>
                      <span>Flights</span>
                    </div>
                    <div class="inclusion-item">
                      <i class="fas fa-hotel"></i>
                      <span>Hotels</span>
                    </div>
                    <div class="inclusion-item">
                      <i class="fas fa-utensils"></i>
                      <span>Meals</span>
                    </div>
                    <div class="inclusion-item">
                      <i class="fas fa-mountain"></i>
                      <span>Adventure</span>
                    </div>
                  </div>

                  <div class="emi-highlight">
                    <div class="emi-amount">₹2,799<span class="emi-period">/month</span></div>
                    <div class="emi-details">for 6 Prepaid EMIs</div>
                    <div class="total-amount">Total: ₹16,794</div>
                  </div>

                  <button class="view-details-btn" onclick="openPackageModal('manali-adventure')">
                    View Details
                  </button>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- Features Section -->
        <section class="features-section">
          <div class="features-container">
            <div class="features-header">
              <h2 class="features-title">Why Family EMI Packages</h2>
              <p class="features-subtitle">Experience hassle-free family vacations with flexible payment options</p>
            </div>

            <div class="features-grid">
              <div class="feature-card">
                <div class="feature-icon">
                  <img src="img/vector-6.svg" alt="Easy Plan Swap" />
                </div>
                <h3 class="feature-title">Easy Plan Swap</h3>
                <p class="feature-description">
                  Swap plans 60 days before the travel date, change destinations or upgrade plans.
                </p>
              </div>

              <div class="feature-card">
                <div class="feature-icon">
                  <img src="img/vector-4.svg" alt="No Last Minute Rush" />
                </div>
                <h3 class="feature-title">No Last Minute Rush</h3>
                <p class="feature-description">
                  Enjoy your family vacation without the stress of peak season crowds.
                </p>
              </div>

              <div class="feature-card">
                <div class="feature-icon">
                  <img src="img/vector-2.svg" alt="Guaranteed Availability" />
                </div>
                <h3 class="feature-title">Guaranteed Availability</h3>
                <p class="feature-description">
                  Secure your booking without worrying about last-minute availability issues.
                </p>
              </div>

              <div class="feature-card">
                <div class="feature-icon">
                  <img src="img/vector.svg" alt="Rewards on Booking" />
                </div>
                <h3 class="feature-title">Rewards on Booking</h3>
                <p class="feature-description">
                  Earn benefits on each EMI payment and by referring friends or family.
                </p>
              </div>
            </div>
          </div>
        </section>

        <!-- Traveler Selector Modal -->
        <div class="modal-overlay" id="travelerModal">
          <div class="modal-content traveler-modal">
            <div class="modal-header">
              <h3>Select Travelers</h3>
              <button class="modal-close" onclick="closeTravelerModal()">
                <i class="fas fa-times"></i>
              </button>
            </div>

            <div class="modal-body">
              <div class="traveler-counters">
                <div class="counter-group">
                  <div class="counter-info">
                    <span class="counter-label">Adults</span>
                    <span class="counter-sublabel">(12+ years)</span>
                  </div>
                  <div class="counter-controls">
                    <button type="button" class="counter-btn" onclick="updateCounter('adults', -1)">-</button>
                    <span class="counter-value" id="adultsCount">2</span>
                    <button type="button" class="counter-btn" onclick="updateCounter('adults', 1)">+</button>
                  </div>
                </div>

                <div class="counter-group">
                  <div class="counter-info">
                    <span class="counter-label">Children</span>
                    <span class="counter-sublabel">(2-11 years)</span>
                  </div>
                  <div class="counter-controls">
                    <button type="button" class="counter-btn" onclick="updateCounter('children', -1)">-</button>
                    <span class="counter-value" id="childrenCount">0</span>
                    <button type="button" class="counter-btn" onclick="updateCounter('children', 1)">+</button>
                  </div>
                </div>

                <div class="counter-group">
                  <div class="counter-info">
                    <span class="counter-label">Infants</span>
                    <span class="counter-sublabel">(Under 2 years)</span>
                  </div>
                  <div class="counter-controls">
                    <button type="button" class="counter-btn" onclick="updateCounter('infants', -1)">-</button>
                    <span class="counter-value" id="infantsCount">0</span>
                    <button type="button" class="counter-btn" onclick="updateCounter('infants', 1)">+</button>
                  </div>
                </div>
              </div>

              <div class="detected-family-type">
                <span class="family-type-label">Detected Family Type:</span>
                <span class="family-type-value" id="modalFamilyType">Stellar Duo (2 Adults)</span>
              </div>
            </div>

            <div class="modal-footer">
              <button class="apply-btn" onclick="applyTravelerSelection()">
                Apply Selection
              </button>
            </div>
          </div>
        </div>

        <!-- Package Details Modal -->
        <div class="modal-overlay" id="packageModal">
          <div class="modal-content package-modal">
            <div class="modal-header">
              <h3 id="packageModalTitle">Kashmir Winter Wonderland</h3>
              <button class="modal-close" onclick="closePackageModal()">
                <i class="fas fa-times"></i>
              </button>
            </div>

            <div class="modal-body">
              <!-- Image Gallery -->
              <div class="image-gallery">
                <div class="main-image">
                  <img id="packageMainImage" src="img/rectangle-14.png" alt="Package Image" />
                </div>
              </div>

              <!-- Package Tabs -->
              <div class="package-tabs">
                <button class="tab-btn active" onclick="switchTab('overview', this)">Overview</button>
                <button class="tab-btn" onclick="switchTab('itinerary', this)">Itinerary</button>
                <button class="tab-btn" onclick="switchTab('emi-options', this)">EMI Options</button>
              </div>

              <!-- Tab Content -->
              <div class="tab-content">
                <!-- Overview Tab -->
                <div class="tab-pane active" id="overview">
                  <div class="package-overview">
                    <div class="overview-item">
                      <strong>Duration:</strong> 5 Nights / 6 Days
                    </div>
                    <div class="overview-item">
                      <strong>Highlights:</strong> Dal Lake, Gulmarg, Pahalgam, Srinagar
                    </div>
                    <div class="overview-item">
                      <strong>Inclusions:</strong>
                      <ul>
                        <li>Round-trip flights</li>
                        <li>4-star hotel accommodation</li>
                        <li>Daily breakfast and dinner</li>
                        <li>All sightseeing and transfers</li>
                        <li>Professional guide</li>
                      </ul>
                    </div>
                  </div>
                </div>

                <!-- Itinerary Tab -->
                <div class="tab-pane" id="itinerary">
                  <div class="itinerary-content">
                    <div class="day-item">
                      <h4>Day 1: Arrival in Srinagar</h4>
                      <p>Arrive at Srinagar airport, transfer to houseboat, Dal Lake shikara ride</p>
                    </div>
                    <div class="day-item">
                      <h4>Day 2: Srinagar to Gulmarg</h4>
                      <p>Drive to Gulmarg, Gondola ride, snow activities</p>
                    </div>
                    <div class="day-item">
                      <h4>Day 3: Gulmarg to Pahalgam</h4>
                      <p>Transfer to Pahalgam, Betaab Valley, Aru Valley visit</p>
                    </div>
                  </div>
                </div>

                <!-- EMI Options Tab -->
                <div class="tab-pane" id="emi-options">
                  <div class="emi-plans">
                    <div class="emi-plan">
                      <div class="plan-header">
                        <h4>3 Months</h4>
                        <span class="plan-label quick-pay">Quick Pay</span>
                      </div>
                      <div class="plan-amount">₹13,997/month</div>
                      <div class="plan-details">
                        <div>Total: ₹41,991</div>
                        <div>Processing Fee: ₹999</div>
                      </div>
                      <button class="select-plan-btn">Select Plan</button>
                    </div>

                    <div class="emi-plan best-value">
                      <div class="plan-header">
                        <h4>6 Months</h4>
                        <span class="plan-label">Best Value</span>
                      </div>
                      <div class="plan-amount">₹6,998/month</div>
                      <div class="plan-details">
                        <div>Total: ₹41,988</div>
                        <div>Processing Fee: ₹996</div>
                      </div>
                      <button class="select-plan-btn">Select Plan</button>
                    </div>

                    <div class="emi-plan">
                      <div class="plan-header">
                        <h4>12 Months</h4>
                        <span class="plan-label low-monthly">Low Monthly</span>
                      </div>
                      <div class="plan-amount">₹3,499/month</div>
                      <div class="plan-details">
                        <div>Total: ₹41,988</div>
                        <div>Processing Fee: ₹996</div>
                      </div>
                      <button class="select-plan-btn">Select Plan</button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- JavaScript -->
        <script>
          // Family Type Data (matching the 34 family types from database)
          const familyTypes = {
            'baby-bliss': { name: 'Baby Bliss', composition: '2 Adults + 1 Infant (Below 2 yrs)' },
            'tiny-delight': { name: 'Tiny Delight', composition: '2 Adults + 1 Child (Below 5 yrs)' },
            'family-nest': { name: 'Family Nest', composition: '2 Adults + 2 Children (Below 5 yrs)' },
            'stellar-duo': { name: 'Stellar Duo', composition: '2 Adults' },
            'fantastic-four': { name: 'Fantastic Four', composition: '4 Adults' },
            'grand-bliss': { name: 'Grand Bliss', composition: '2 Adults + 2 Children + 2 Grandparents' }
          };

          // Destination Data
          const destinations = [
            'Kashmir', 'Goa', 'Manali', 'Kerala', 'Rajasthan', 'Himachal Pradesh',
            'Meghalaya', 'Ooty', 'Alleppey', 'Darjeeling', 'Shimla', 'Udaipur'
          ];

          // Current traveler counts
          let travelers = { adults: 2, children: 0, infants: 0 };

          // Initialize the page
          document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM Content Loaded - Initializing TripXplo Family EMI');
            setupDestinationAutocomplete();
            setupFormSubmission();
            updateFamilyTypeDisplay();
            console.log('Initialization complete');
          });

          // Setup destination autocomplete
          function setupDestinationAutocomplete() {
            const destinationInput = document.getElementById('destination');
            const suggestionsDiv = document.getElementById('destinationSuggestions');

            destinationInput.addEventListener('input', function() {
              const value = this.value.toLowerCase();
              const filtered = destinations.filter(dest =>
                dest.toLowerCase().includes(value)
              );

              if (value && filtered.length > 0) {
                suggestionsDiv.innerHTML = filtered.map(dest =>
                  `<div class="suggestion-item" onclick="selectDestination('${dest}')">${dest}</div>`
                ).join('');
                suggestionsDiv.style.display = 'block';
              } else {
                suggestionsDiv.style.display = 'none';
              }
            });
          }

          // Select destination from suggestions
          function selectDestination(destination) {
            document.getElementById('destination').value = destination;
            document.getElementById('destinationSuggestions').style.display = 'none';
          }

          // Setup form submission
          function setupFormSubmission() {
            document.getElementById('familySearchForm').addEventListener('submit', function(e) {
              e.preventDefault();
              searchPackages();
            });

            document.getElementById('travelerSelector').addEventListener('click', function() {
              openTravelerModal();
            });
          }

          // Search packages
          function searchPackages() {
            const destination = document.getElementById('destination').value;
            const travelDate = document.getElementById('travelDate').value;

            if (!destination || !travelDate) {
              alert('Please fill in all fields');
              return;
            }

            // Update results header
            const familyType = detectFamilyType();
            const dateObj = new Date(travelDate + '-01');
            const monthYear = dateObj.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });

            document.getElementById('resultsTitle').innerHTML = `
              Showing packages for <span class="family-type-highlight">${familyType.name}</span>
              to <span class="destination-highlight">${destination}</span> in <span class="date-highlight">${monthYear}</span>
            `;

            // Show results section
            document.getElementById('resultsSection').style.display = 'block';
            document.getElementById('resultsSection').scrollIntoView({ behavior: 'smooth' });
          }

          // Detect family type based on traveler counts
          function detectFamilyType() {
            const { adults, children, infants } = travelers;

            if (adults === 2 && children === 0 && infants === 1) {
              return familyTypes['baby-bliss'];
            } else if (adults === 2 && children === 1 && infants === 0) {
              return familyTypes['tiny-delight'];
            } else if (adults === 2 && children === 2 && infants === 0) {
              return familyTypes['family-nest'];
            } else if (adults === 2 && children === 0 && infants === 0) {
              return familyTypes['stellar-duo'];
            } else if (adults === 4 && children === 0 && infants === 0) {
              return familyTypes['fantastic-four'];
            } else {
              return { name: 'Custom Family', composition: `${adults} Adults${children > 0 ? ` + ${children} Children` : ''}${infants > 0 ? ` + ${infants} Infants` : ''}` };
            }
          }

          // Update family type display
          function updateFamilyTypeDisplay() {
            const familyType = detectFamilyType();
            document.getElementById('detectedFamilyType').textContent = familyType.name;
            document.getElementById('travelerDisplay').textContent = familyType.composition;

            if (document.getElementById('modalFamilyType')) {
              document.getElementById('modalFamilyType').textContent = familyType.name;
            }
          }

          // Traveler Modal Functions
          function openTravelerModal() {
            document.getElementById('travelerModal').style.display = 'flex';
          }

          function closeTravelerModal() {
            document.getElementById('travelerModal').style.display = 'none';
          }

          function updateCounter(type, change) {
            travelers[type] = Math.max(0, travelers[type] + change);
            if (type === 'adults') travelers[type] = Math.max(1, travelers[type]); // At least 1 adult

            document.getElementById(type + 'Count').textContent = travelers[type];
            updateFamilyTypeDisplay();
          }

          function applyTravelerSelection() {
            updateFamilyTypeDisplay();
            closeTravelerModal();
          }

          // Package Modal Functions
          function openPackageModal(packageId) {
            document.getElementById('packageModal').style.display = 'flex';
          }

          function closePackageModal() {
            document.getElementById('packageModal').style.display = 'none';
          }

          function switchTab(tabName, element) {
            // Remove active class from all tabs and panes
            document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
            document.querySelectorAll('.tab-pane').forEach(pane => pane.classList.remove('active'));

            // Add active class to clicked tab and corresponding pane
            if (element) {
              element.classList.add('active');
            }
            const targetPane = document.getElementById(tabName);
            if (targetPane) {
              targetPane.classList.add('active');
            }
          }

          // Close modals when clicking outside
          window.addEventListener('click', function(event) {
            if (event.target.classList.contains('modal-overlay')) {
              event.target.style.display = 'none';
            }
          });
        </script>
      </div>
    </div>
  </body>
</html>
