import React, { useState, useEffect } from 'react';
import { 
  Users, 
  Plus, 
  Edit, 
  Trash2, 
  Search, 
  Filter,
  Car,
  Bed,
  Baby,
  User,
  Save,
  X
} from 'lucide-react';

interface FamilyType {
  id: string;
  family_id: string;
  family_type: string;
  no_of_adults: number;
  no_of_children: number;
  no_of_child: number;
  no_of_infants: number;
  family_count: number;
  rooms_need: number;
  cab_type: string;
  cab_capacity: number;
  created_at: string;
  updated_at: string;
}

const FamilyTypeManagement: React.FC = () => {
  const [familyTypes, setFamilyTypes] = useState<FamilyType[]>([]);
  const [filteredFamilyTypes, setFilteredFamilyTypes] = useState<FamilyType[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFamilyType, setSelectedFamilyType] = useState<FamilyType | null>(null);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [formData, setFormData] = useState<Partial<FamilyType>>({
    family_type: '',
    no_of_adults: 2,
    no_of_children: 0,
    no_of_child: 0,
    no_of_infants: 0,
    family_count: 1,
    rooms_need: 1,
    cab_type: 'Sedan',
    cab_capacity: 4
  });

  useEffect(() => {
    loadFamilyTypes();
  }, []);

  useEffect(() => {
    let filtered = familyTypes;

    if (searchQuery) {
      filtered = filtered.filter(
        ft => 
          ft.family_type.toLowerCase().includes(searchQuery.toLowerCase()) ||
          ft.family_id.toLowerCase().includes(searchQuery.toLowerCase()) ||
          ft.cab_type.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    setFilteredFamilyTypes(filtered);
  }, [familyTypes, searchQuery]);

  const loadFamilyTypes = async () => {
    try {
      setLoading(true);
      // API call to fetch family types
      const mockData: FamilyType[] = [
        {
          id: '1',
          family_id: 'COUPLE',
          family_type: 'Couple',
          no_of_adults: 2,
          no_of_children: 0,
          no_of_child: 0,
          no_of_infants: 0,
          family_count: 2,
          rooms_need: 1,
          cab_type: 'Sedan',
          cab_capacity: 4,
          created_at: '2024-01-10',
          updated_at: '2024-01-10'
        },
        {
          id: '2',
          family_id: 'FAMILY_4',
          family_type: 'Small Family (2+2)',
          no_of_adults: 2,
          no_of_children: 2,
          no_of_child: 0,
          no_of_infants: 0,
          family_count: 4,
          rooms_need: 1,
          cab_type: 'SUV',
          cab_capacity: 7,
          created_at: '2024-01-10',
          updated_at: '2024-01-10'
        }
      ];
      
      setFamilyTypes(mockData);
    } catch (error) {
      console.error('Error loading family types:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async (data: Partial<FamilyType>) => {
    try {
      if (selectedFamilyType) {
        // Update existing
        console.log('Updating family type:', { ...selectedFamilyType, ...data });
      } else {
        // Create new
        console.log('Creating family type:', data);
      }
      
      setIsAddModalOpen(false);
      setIsEditModalOpen(false);
      setSelectedFamilyType(null);
      setFormData({
        family_type: '',
        no_of_adults: 2,
        no_of_children: 0,
        no_of_child: 0,
        no_of_infants: 0,
        family_count: 1,
        rooms_need: 1,
        cab_type: 'Sedan',
        cab_capacity: 4
      });
      loadFamilyTypes();
    } catch (error) {
      console.error('Error saving family type:', error);
    }
  };

  const handleDelete = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this family type?')) {
      try {
        console.log('Deleting family type:', id);
        loadFamilyTypes();
      } catch (error) {
        console.error('Error deleting family type:', error);
      }
    }
  };

  const handleEdit = (familyType: FamilyType) => {
    setSelectedFamilyType(familyType);
    setFormData(familyType);
    setIsEditModalOpen(true);
  };

  const handleAdd = () => {
    setSelectedFamilyType(null);
    setFormData({
      family_type: '',
      no_of_adults: 2,
      no_of_children: 0,
      no_of_child: 0,
      no_of_infants: 0,
      family_count: 1,
      rooms_need: 1,
      cab_type: 'Sedan',
      cab_capacity: 4
    });
    setIsAddModalOpen(true);
  };

  const getTotalPeople = (ft: FamilyType) => {
    return ft.no_of_adults + ft.no_of_children + ft.no_of_child + ft.no_of_infants;
  };

  const formatComposition = (ft: FamilyType) => {
    const parts = [];
    if (ft.no_of_adults > 0) parts.push(`${ft.no_of_adults}A`);
    if (ft.no_of_children > 0) parts.push(`${ft.no_of_children}C`);
    if (ft.no_of_child > 0) parts.push(`${ft.no_of_child}K`);
    if (ft.no_of_infants > 0) parts.push(`${ft.no_of_infants}I`);
    return parts.join(' + ');
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">Family Type Management</h1>
          <p className="text-gray-600">Manage family configurations for pricing and quotes</p>
        </div>
        <button
          onClick={handleAdd}
          className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors flex items-center gap-2"
        >
          <Plus className="w-4 h-4" />
          Add Family Type
        </button>
      </div>

      {/* Search and Filters */}
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 mb-6">
        <div className="flex gap-4 items-center">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search by family type, ID, or vehicle..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
            />
          </div>
          <div className="flex items-center text-sm text-gray-600">
            <Filter className="w-4 h-4 mr-2" />
            Showing {filteredFamilyTypes.length} of {familyTypes.length} family types
          </div>
        </div>
      </div>

      {/* Family Types Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredFamilyTypes.map((familyType) => (
          <div key={familyType.id} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
            <div className="p-6">
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h3 className="text-lg font-semibold text-gray-800">{familyType.family_type}</h3>
                  <p className="text-sm text-gray-500">{familyType.family_id}</p>
                </div>
                <div className="flex gap-2">
                  <button
                    onClick={() => handleEdit(familyType)}
                    className="p-1 text-blue-600 hover:text-blue-800"
                  >
                    <Edit className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => handleDelete(familyType.id)}
                    className="p-1 text-red-600 hover:text-red-800"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Users className="w-4 h-4 text-gray-500" />
                    <span className="text-sm font-medium">Composition:</span>
                  </div>
                  <span className="text-sm text-gray-600">{formatComposition(familyType)}</span>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <User className="w-4 h-4 text-gray-500" />
                    <span className="text-sm font-medium">Total People:</span>
                  </div>
                  <span className="text-sm text-gray-600">{getTotalPeople(familyType)}</span>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Bed className="w-4 h-4 text-gray-500" />
                    <span className="text-sm font-medium">Rooms:</span>
                  </div>
                  <span className="text-sm text-gray-600">{familyType.rooms_need}</span>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Car className="w-4 h-4 text-gray-500" />
                    <span className="text-sm font-medium">Vehicle:</span>
                  </div>
                  <span className="text-sm text-gray-600">{familyType.cab_type} ({familyType.cab_capacity} seater)</span>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Add/Edit Modal */}
      {(isAddModalOpen || isEditModalOpen) && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-bold text-gray-800">
                  {selectedFamilyType ? 'Edit Family Type' : 'Add New Family Type'}
                </h2>
                <button
                  onClick={() => {
                    setIsAddModalOpen(false);
                    setIsEditModalOpen(false);
                  }}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="w-6 h-6" />
                </button>
              </div>

              <form onSubmit={(e) => { e.preventDefault(); handleSave(formData); }}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Family Type Name
                    </label>
                    <input
                      type="text"
                      value={formData.family_type || ''}
                      onChange={(e) => setFormData({...formData, family_type: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Family ID
                    </label>
                    <input
                      type="text"
                      value={formData.family_id || ''}
                      onChange={(e) => setFormData({...formData, family_id: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                      placeholder="e.g., COUPLE, FAMILY_4"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Adults
                    </label>
                    <input
                      type="number"
                      value={formData.no_of_adults || 0}
                      onChange={(e) => setFormData({...formData, no_of_adults: Number(e.target.value)})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                      min="1"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Children (6-12 years)
                    </label>
                    <input
                      type="number"
                      value={formData.no_of_children || 0}
                      onChange={(e) => setFormData({...formData, no_of_children: Number(e.target.value)})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                      min="0"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Kids (Below 5 years)
                    </label>
                    <input
                      type="number"
                      value={formData.no_of_child || 0}
                      onChange={(e) => setFormData({...formData, no_of_child: Number(e.target.value)})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                      min="0"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Infants (Below 2 years)
                    </label>
                    <input
                      type="number"
                      value={formData.no_of_infants || 0}
                      onChange={(e) => setFormData({...formData, no_of_infants: Number(e.target.value)})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                      min="0"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Rooms Needed
                    </label>
                    <input
                      type="number"
                      value={formData.rooms_need || 0}
                      onChange={(e) => setFormData({...formData, rooms_need: Number(e.target.value)})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                      min="1"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Vehicle Type
                    </label>
                    <select
                      value={formData.cab_type || ''}
                      onChange={(e) => setFormData({...formData, cab_type: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                      required
                    >
                      <option value="Sedan">Sedan</option>
                      <option value="SUV">SUV</option>
                      <option value="MUV">MUV</option>
                      <option value="Hatchback">Hatchback</option>
                      <option value="Tempo Traveller">Tempo Traveller</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Vehicle Capacity
                    </label>
                    <input
                      type="number"
                      value={formData.cab_capacity || 0}
                      onChange={(e) => setFormData({...formData, cab_capacity: Number(e.target.value)})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                      min="1"
                      required
                    />
                  </div>
                </div>

                <div className="mt-6 flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={() => {
                      setIsAddModalOpen(false);
                      setIsEditModalOpen(false);
                    }}
                    className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors flex items-center gap-2"
                  >
                    <Save className="w-4 h-4" />
                    {selectedFamilyType ? 'Update' : 'Create'} Family Type
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default FamilyTypeManagement; 