/* Reset and Base Styles - Override globals.css reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  margin: 0 !important;
  padding: 0 !important;
  height: 100% !important;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif !important;
  line-height: 1.6 !important;
  color: var(--black) !important;
  background-color: var(--white) !important;
}

/* Override reset styles */
h1, h2, h3, h4, h5, h6 {
  font-weight: inherit !important;
  font-size: inherit !important;
  margin: 0 !important;
  padding: 0 !important;
}

p {
  margin: 0 !important;
  padding: 0 !important;
}

button {
  font-family: inherit !important;
  cursor: pointer !important;
}

input {
  font-family: inherit !important;
}

.family-emi-website {
  min-height: 100vh !important;
  background: linear-gradient(135deg, #8B5CF6 0%, #3B82F6 100%) !important;
  position: relative !important;
  overflow-x: hidden !important;
  width: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Navigation */
.navigation {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  z-index: 1000 !important;
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(10px) !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
  width: 100% !important;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  font-size: 1.5rem;
  font-weight: 800;
  letter-spacing: 1px;
}

.logo-trip {
  color: var(--green);
}

.logo-xplo {
  color: var(--x-1st);
}

.nav-links {
  display: flex;
  gap: 2rem;
}

.nav-links a {
  text-decoration: none;
  color: var(--black);
  font-weight: 500;
  transition: color 0.3s ease;
}

.nav-links a:hover {
  color: var(--x-2nd);
}

/* Hero Section */
.hero-section {
  min-height: 100vh !important;
  display: flex !important;
  flex-direction: column !important;
  justify-content: center !important;
  align-items: center !important;
  padding: 8rem 2rem 4rem !important;
  position: relative !important;
  overflow: hidden !important;
  background: linear-gradient(135deg, #8B5CF6 0%, #3B82F6 100%) !important;
  width: 100% !important;
}

.hero-content {
  max-width: 800px;
  width: 100%;
  text-align: center;
  z-index: 10;
}

.hero-text {
  margin-bottom: 3rem;
}

.hero-title {
  font-size: 3.5rem !important;
  font-weight: 700 !important;
  color: white !important;
  margin-bottom: 1rem !important;
  line-height: 1.2 !important;
  text-align: center !important;
  font-family: 'Inter', sans-serif !important;
}

.hero-subtitle {
  font-size: 1.25rem !important;
  color: rgba(255, 255, 255, 0.9) !important;
  margin-bottom: 2rem !important;
  text-align: center !important;
  font-family: 'Inter', sans-serif !important;
}

/* Search Form */
.search-form-container {
  background: white !important;
  border-radius: 20px !important;
  padding: 2rem !important;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1) !important;
  margin-bottom: 1rem !important;
  width: 100% !important;
  max-width: 800px !important;
}

.search-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 1.5rem;
}

.input-group {
  position: relative;
}

.input-label {
  display: block;
  font-weight: 600;
  color: var(--text-clr);
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.input-label i {
  margin-right: 0.5rem;
  color: var(--x-2nd);
}

.form-input {
  width: 100%;
  padding: 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: white;
}

.form-input:focus {
  outline: none;
  border-color: var(--x-2nd);
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

.traveler-selector {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  text-align: left;
}

.destination-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 100;
  display: none;
}

.suggestion-item {
  padding: 0.75rem 1rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.suggestion-item:hover {
  background-color: #f3f4f6;
}

.search-btn {
  background: linear-gradient(135deg, var(--x-2nd) 0%, var(--x-1st) 100%);
  color: white;
  border: none;
  padding: 1.25rem 2rem;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.search-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(139, 92, 246, 0.3);
}

.family-type-display {
  text-align: center;
  padding: 1rem;
  background: rgba(139, 92, 246, 0.1);
  border-radius: 12px;
  margin-top: 1rem;
}

.family-type-label {
  font-weight: 600;
  color: var(--text-clr);
  margin-right: 0.5rem;
}

.family-type-name {
  font-weight: 700;
  color: var(--x-2nd);
}

/* Hero Background */
.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.hero-plane {
  position: absolute;
  width: 60px;
  height: auto;
  opacity: 0.3;
  animation: float 6s ease-in-out infinite;
}

.hero-plane-1 {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.hero-plane-2 {
  top: 60%;
  right: 15%;
  animation-delay: 3s;
}

.hero-decoration {
  position: absolute;
  width: 400px;
  height: 400px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  top: 10%;
  right: -200px;
  filter: blur(100px);
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

/* Results Section */
.results-section {
  background: white;
  padding: 4rem 2rem;
  min-height: 100vh;
}

.results-container {
  max-width: 1200px;
  margin: 0 auto;
}

.results-header {
  text-align: center;
  margin-bottom: 3rem;
}

.results-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--black);
  line-height: 1.3;
}

.family-type-highlight {
  color: var(--x-2nd);
}

.destination-highlight {
  color: var(--green);
}

.date-highlight {
  color: var(--x-4);
}

/* Package Grid */
.package-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.package-card {
  background: white;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 1px solid #f1f5f9;
}

.package-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.package-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.package-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.package-card:hover .package-image img {
  transform: scale(1.05);
}

.duration-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
}

.offer-badge {
  position: absolute;
  top: 1rem;
  left: 1rem;
  background: var(--green);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.offer-badge.early-bird {
  background: var(--yellow);
  color: var(--black);
}

.offer-badge.best-value {
  background: var(--x-4);
}

.package-content {
  padding: 1.5rem;
}

.package-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--black);
  margin-bottom: 1rem;
}

.package-inclusions {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.inclusion-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: var(--text-clr);
}

.inclusion-item i {
  color: var(--x-2nd);
  font-size: 1rem;
}

.emi-highlight {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%);
  padding: 1.5rem;
  border-radius: 12px;
  margin-bottom: 1.5rem;
  text-align: center;
}

.emi-amount {
  font-size: 2rem;
  font-weight: 800;
  color: var(--x-2nd);
  margin-bottom: 0.5rem;
}

.emi-period {
  font-size: 1rem;
  font-weight: 500;
  color: var(--text-clr);
}

.emi-details {
  font-size: 1rem;
  color: var(--text-clr);
  margin-bottom: 0.5rem;
}

.total-amount {
  font-size: 0.9rem;
  color: var(--text-2);
}

.view-details-btn {
  width: 100%;
  padding: 1rem;
  background: transparent;
  border: 2px solid var(--x-2nd);
  color: var(--x-2nd);
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.view-details-btn:hover {
  background: var(--x-2nd);
  color: white;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: none;
  justify-content: center;
  align-items: center;
  z-index: 2000;
  padding: 2rem;
}

.modal-content {
  background: white;
  border-radius: 20px;
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem 2rem 1rem;
  border-bottom: 1px solid #f1f5f9;
}

.modal-header h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--black);
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--text-clr);
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.modal-close:hover {
  background: #f1f5f9;
  color: var(--black);
}

.modal-body {
  padding: 2rem;
}

.modal-footer {
  padding: 1rem 2rem 2rem;
  border-top: 1px solid #f1f5f9;
}

/* Traveler Modal */
.traveler-modal {
  max-width: 500px;
}

.traveler-counters {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.counter-group {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
}

.counter-info {
  display: flex;
  flex-direction: column;
}

.counter-label {
  font-weight: 600;
  color: var(--black);
  font-size: 1rem;
}

.counter-sublabel {
  font-size: 0.85rem;
  color: var(--text-clr);
}

.counter-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.counter-btn {
  width: 40px;
  height: 40px;
  border: 2px solid var(--x-2nd);
  background: white;
  color: var(--x-2nd);
  border-radius: 50%;
  font-size: 1.2rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.counter-btn:hover {
  background: var(--x-2nd);
  color: white;
}

.counter-value {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--black);
  min-width: 30px;
  text-align: center;
}

.detected-family-type {
  background: rgba(139, 92, 246, 0.1);
  padding: 1rem;
  border-radius: 12px;
  text-align: center;
}

.family-type-label {
  font-weight: 600;
  color: var(--text-clr);
  margin-right: 0.5rem;
}

.family-type-value {
  font-weight: 700;
  color: var(--x-2nd);
}

.apply-btn {
  width: 100%;
  padding: 1rem;
  background: linear-gradient(135deg, var(--x-2nd) 0%, var(--x-1st) 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.apply-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(139, 92, 246, 0.3);
}

/* Package Modal */
.package-modal {
  max-width: 800px;
}

.image-gallery {
  margin-bottom: 2rem;
}

.main-image {
  width: 100%;
  height: 300px;
  border-radius: 12px;
  overflow: hidden;
}

.main-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.package-tabs {
  display: flex;
  border-bottom: 2px solid #f1f5f9;
  margin-bottom: 2rem;
}

.tab-btn {
  flex: 1;
  padding: 1rem;
  background: none;
  border: none;
  font-weight: 600;
  color: var(--text-clr);
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 3px solid transparent;
}

.tab-btn.active {
  color: var(--x-2nd);
  border-bottom-color: var(--x-2nd);
}

.tab-content {
  min-height: 300px;
}

.tab-pane {
  display: none;
}

.tab-pane.active {
  display: block;
}

.package-overview {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.overview-item {
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
}

.overview-item strong {
  color: var(--black);
  margin-right: 0.5rem;
}

.overview-item ul {
  margin-top: 0.5rem;
  margin-left: 1rem;
}

.overview-item li {
  margin-bottom: 0.25rem;
  color: var(--text-clr);
}

.itinerary-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.day-item {
  padding: 1.5rem;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
}

.day-item h4 {
  color: var(--x-2nd);
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
}

.day-item p {
  color: var(--text-clr);
  line-height: 1.6;
}

.emi-plans {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.emi-plan {
  border: 2px solid #e5e7eb;
  border-radius: 16px;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
}

.emi-plan.best-value {
  border-color: var(--green);
  background: rgba(50, 214, 159, 0.05);
}

.emi-plan:hover {
  border-color: var(--x-2nd);
  transform: translateY(-4px);
}

.plan-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.plan-header h4 {
  font-size: 1.1rem;
  font-weight: 700;
  color: var(--black);
}

.plan-label {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-weight: 600;
}

.plan-label.quick-pay {
  background: var(--yellow);
  color: var(--black);
}

.plan-label:not(.quick-pay):not(.low-monthly) {
  background: var(--green);
  color: white;
}

.plan-label.low-monthly {
  background: var(--x-4);
  color: white;
}

.plan-amount {
  font-size: 1.5rem;
  font-weight: 800;
  color: var(--x-2nd);
  margin-bottom: 1rem;
}

.plan-details {
  margin-bottom: 1.5rem;
  font-size: 0.9rem;
  color: var(--text-clr);
}

.plan-details div {
  margin-bottom: 0.25rem;
}

.select-plan-btn {
  width: 100%;
  padding: 0.75rem;
  background: var(--x-2nd);
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.select-plan-btn:hover {
  background: var(--x-1st);
  transform: translateY(-2px);
}

/* Features Section */
.features-section {
  background: #f8fafc;
  padding: 4rem 2rem;
}

.features-container {
  max-width: 1200px;
  margin: 0 auto;
}

.features-header {
  text-align: center;
  margin-bottom: 3rem;
}

.features-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--black);
  margin-bottom: 1rem;
}

.features-subtitle {
  font-size: 1.2rem;
  color: var(--text-clr);
  max-width: 600px;
  margin: 0 auto;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
}

.feature-card {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 1px solid #f1f5f9;
}

.feature-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.feature-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 1.5rem;
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.feature-icon img {
  width: 40px;
  height: 40px;
  filter: brightness(0) saturate(100%) invert(45%) sepia(84%) saturate(2482%) hue-rotate(244deg) brightness(98%) contrast(95%);
}

.feature-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--black);
  margin-bottom: 1rem;
}

.feature-description {
  color: var(--text-clr);
  line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-container {
    padding: 1rem;
  }

  .nav-links {
    display: none;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.1rem;
  }

  .search-form-container {
    padding: 1.5rem;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .package-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .results-title {
    font-size: 1.5rem;
  }

  .modal-overlay {
    padding: 1rem;
  }

  .modal-content {
    max-height: 95vh;
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 1.5rem;
  }

  .emi-plans {
    grid-template-columns: 1fr;
  }

  .counter-group {
    padding: 0.75rem;
  }

  .hero-plane {
    display: none;
  }
}

@media (max-width: 480px) {
  .hero-section {
    padding: 6rem 1rem 2rem;
  }

  .hero-title {
    font-size: 2rem;
  }

  .search-form-container {
    padding: 1rem;
  }

  .package-card {
    margin: 0 0.5rem;
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 1rem;
  }
}

/* Utility Classes */
.text-center {
  text-align: center;
}

.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }

.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }

.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 1rem; }
.p-4 { padding: 1.5rem; }

.font-bold { font-weight: 700; }
.font-semibold { font-weight: 600; }
.font-medium { font-weight: 500; }

.text-sm { font-size: 0.875rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.slide-up {
  animation: slideUp 0.5s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Focus States for Accessibility */
.form-input:focus,
.search-btn:focus,
.view-details-btn:focus,
.apply-btn:focus,
.select-plan-btn:focus,
.counter-btn:focus,
.tab-btn:focus {
  outline: 2px solid var(--x-2nd);
  outline-offset: 2px;
}

/* Loading States */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid var(--x-2nd);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Hide all old travel-agency styles */
.travel-agency * {
  display: none !important;
}

/* Show only our new family-emi-website styles */
.family-emi-website,
.family-emi-website * {
  display: initial !important;
}

/* End of CSS file */
  position: absolute;
  width: 479px;
  height: 497px;
  top: 17px;
  left: 271px;
  background-color: var(--d-2);
  border-radius: 239.47px/248.43px;
  transform: rotate(180deg);
  filter: blur(150px);
  opacity: 0.4;
}

.travel-agency .najam-centre {
  position: absolute;
  top: 0;
  left: 0;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
  color: #5e6282;
  font-size: 14px;
  letter-spacing: 0;
  line-height: 21px;
}

.travel-agency .outbound {
  position: absolute;
  width: 111px;
  height: 71px;
  top: 21px;
  left: 850px;
}

.travel-agency .social {
  position: absolute;
  width: 131px;
  height: 65px;
  top: -8px;
  left: -10px;
}

.travel-agency .overlap {
  position: absolute;
  width: 257px;
  height: 117px;
  top: 0;
  left: 0;
}

.travel-agency .company-desc {
  position: absolute;
  width: 255px;
  height: 117px;
  top: 0;
  left: 2px;
}

.travel-agency .group {
  position: absolute;
  width: 183px;
  height: 53px;
  top: 0;
  left: 0;
}

.travel-agency .rectangle {
  position: absolute;
  width: 84px;
  height: 12px;
  top: 41px;
  left: 89px;
  background-color: #fff1da;
  border-radius: 7px;
}

.travel-agency .div {
  position: absolute;
  width: 20px;
  height: 12px;
  top: 0;
  left: 163px;
  background-color: #fff1da;
  border-radius: 7px;
}

.travel-agency .rectangle-2 {
  position: absolute;
  width: 20px;
  height: 12px;
  top: 41px;
  left: 0;
  background-color: #fff1da;
  border-radius: 7px;
}

.travel-agency .make-your-dream {
  position: absolute;
  top: 85px;
  left: 0;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
  color: var(--text-clr);
  font-size: 13px;
  letter-spacing: 0;
  line-height: 16.2px;
}

.travel-agency .TRIPMILESTONE {
  position: absolute;
  top: 25px;
  left: 0;
  font-family: "Poppins", Helvetica;
  font-weight: 800;
  color: transparent;
  font-size: 26px;
  letter-spacing: 1.56px;
  line-height: normal;
}

.travel-agency .text-wrapper {
  color: #3dbb74;
}

.travel-agency .span {
  color: #181e4b;
}

.travel-agency .nav-columns {
  width: 534px;
  top: 21px;
  left: 292px;
  position: absolute;
  height: 82px;
}

.travel-agency .nav-row {
  width: 112px;
  top: 0;
  left: 0;
  position: absolute;
  height: 82px;
}

.travel-agency .text-wrapper-2 {
  position: absolute;
  top: 0;
  left: 0;
  font-family: "Poppins", Helvetica;
  font-weight: 700;
  color: var(--black);
  font-size: 21px;
  letter-spacing: 0;
  line-height: 26.1px;
  white-space: nowrap;
}

.travel-agency .text-wrapper-3 {
  position: absolute;
  top: 60px;
  left: 0;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
  color: var(--text-clr);
  font-size: 18px;
  letter-spacing: 0;
  line-height: 22.4px;
  white-space: nowrap;
}

.travel-agency .nav-row-2 {
  width: 93px;
  height: 80px;
  left: 180px;
  position: absolute;
  top: 0;
}

.travel-agency .text-wrapper-4 {
  position: absolute;
  top: 0;
  left: 1px;
  font-family: "Poppins", Helvetica;
  font-weight: 700;
  color: var(--black);
  font-size: 21px;
  letter-spacing: 0;
  line-height: 26.1px;
  white-space: nowrap;
}

.travel-agency .text-wrapper-5 {
  position: absolute;
  top: 58px;
  left: 0;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
  color: var(--text-clr);
  font-size: 18px;
  letter-spacing: 0;
  line-height: 22.4px;
  white-space: nowrap;
}

.travel-agency .nav-row-3 {
  width: 178px;
  height: 82px;
  left: 360px;
  position: absolute;
  top: 0;
}

.travel-agency .copyrights {
  position: absolute;
  top: 258px;
  left: 432px;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
  color: var(--text-clr);
  font-size: 14px;
  letter-spacing: 0;
  line-height: 17.4px;
  white-space: nowrap;
}

.travel-agency .text-wrapper-6 {
  position: absolute;
  top: 95px;
  left: 870px;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
  color: #5e6282;
  font-size: 14px;
  letter-spacing: 0;
  line-height: 21px;
  white-space: nowrap;
}

.travel-agency .overlap-2 {
  position: absolute;
  width: 1280px;
  height: 4940px;
  top: 47px;
  left: 131px;
}

.travel-agency .book-a-trip {
  position: absolute;
  width: 1047px;
  height: 492px;
  top: 2370px;
  left: 55px;
}

.travel-agency .heading {
  position: absolute;
  top: 42px;
  left: 0;
  font-family: "Volkhov", Helvetica;
  font-weight: 700;
  color: #14183e;
  font-size: 50px;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .image {
  position: absolute;
  width: 414px;
  height: 459px;
  top: 19px;
  left: 629px;
}

.travel-agency .overlap-3 {
  position: relative;
  height: 459px;
}

.travel-agency .group-2 {
  position: absolute;
  width: 354px;
  height: 367px;
  top: 0;
  left: 60px;
  background-color: #59b1e6cc;
  border-radius: 177px/183.5px;
  filter: blur(150px);
  opacity: 0.4;
}

.travel-agency .overlap-wrapper {
  position: absolute;
  width: 370px;
  height: 400px;
  top: 59px;
  left: 0;
}

.travel-agency .overlap-4 {
  position: relative;
  width: 378px;
  height: 400px;
}

.travel-agency .overlap-group-wrapper {
  position: absolute;
  width: 378px;
  height: 400px;
  top: 0;
  left: 0;
}

.travel-agency .overlap-group-2 {
  position: relative;
  width: 370px;
  height: 400px;
  background-color: #ffffff;
  border-radius: 26px;
  box-shadow: 0px 1.85px 3.15px #00000001, 0px 8.15px 6.52px #00000002, 0px 20px 13px #00000003,
    0px 38.52px 25.48px #00000003, 0px 64.81px 46.85px #00000004, 0px 100px 80px #00000005;
}

.travel-agency .OPTIONS {
  position: absolute;
  width: 139px;
  height: 36px;
  top: 283px;
  left: 28px;
}

.travel-agency .LEAF {
  position: absolute;
  width: 36px;
  height: 36px;
  top: 0;
  left: 0;
  background-color: var(--circle);
  border-radius: 18px;
}

.travel-agency .img {
  position: absolute;
  width: 14px;
  height: 14px;
  top: 12px;
  left: 11px;
}

.travel-agency .send {
  position: absolute;
  width: 36px;
  height: 36px;
  top: 0;
  left: 103px;
  background-color: var(--circle);
  border-radius: 18px;
}

.travel-agency .map-icon {
  position: absolute;
  width: 36px;
  height: 36px;
  top: 0;
  left: 54px;
  background-color: var(--circle);
  border-radius: 18px;
}

.travel-agency .map {
  position: absolute;
  width: 14px;
  height: 14px;
  top: 11px;
  left: 10px;
}

.travel-agency .building {
  position: absolute;
  width: 16px;
  height: 16px;
  top: 348px;
  left: 25px;
}

.travel-agency .text-wrapper-7 {
  position: absolute;
  top: 349px;
  left: 56px;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
  color: var(--text-2);
  font-size: 16px;
  letter-spacing: -0.32px;
  line-height: 19.9px;
  white-space: nowrap;
}

.travel-agency .text-wrapper-8 {
  position: absolute;
  top: 243px;
  left: 25px;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
  color: var(--text-clr);
  font-size: 14px;
  letter-spacing: 0;
  line-height: 17.4px;
  white-space: nowrap;
}

.travel-agency .element-adults-children {
  top: 242px;
  left: 124px;
  color: var(--text-2);
  font-size: 16px;
  letter-spacing: -0.08px;
  line-height: 19.9px;
  white-space: nowrap;
  position: absolute;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
}

.travel-agency .rectangle-3 {
  position: absolute;
  width: 321px;
  height: 161px;
  top: 20px;
  left: 25px;
}

.travel-agency .text-wrapper-9 {
  position: absolute;
  top: 207px;
  left: 25px;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
  color: var(--black);
  font-size: 18px;
  letter-spacing: 0.27px;
  line-height: 22.4px;
  white-space: nowrap;
}

.travel-agency .line {
  position: absolute;
  width: 1px;
  height: 16px;
  top: 243px;
  left: 116px;
  object-fit: cover;
}

.travel-agency .heart {
  position: absolute;
  width: 1px;
  height: 1px;
  top: 350px;
  left: 325px;
}

.travel-agency .subheading {
  position: absolute;
  top: 0;
  left: 3px;
  font-family: "Poppins", Helvetica;
  font-weight: 600;
  color: var(--text-clr);
  font-size: 18px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .values {
  position: absolute;
  width: 589px;
  height: 289px;
  top: 203px;
  left: 0;
}

.travel-agency .value {
  position: absolute;
  width: 593px;
  height: 84px;
  top: 0;
  left: 0;
}

.travel-agency .selection-wrapper {
  position: absolute;
  width: 47px;
  height: 48px;
  top: 4px;
  left: 0;
  background-color: var(--x-3);
  border-radius: 13px;
}

.travel-agency .selection {
  position: absolute;
  width: 22px;
  height: 22px;
  top: 13px;
  left: 13px;
}

.travel-agency .text-wrapper-10 {
  position: absolute;
  top: 0;
  left: 68px;
  font-family: "Poppins", Helvetica;
  font-weight: 700;
  color: var(--text-clr);
  font-size: 16px;
  letter-spacing: 0;
  line-height: 19.9px;
  white-space: nowrap;
}

.travel-agency .p {
  position: absolute;
  top: 24px;
  left: 68px;
  font-family: "Poppins", Helvetica;
  font-weight: 400;
  color: var(--text-clr);
  font-size: 16px;
  letter-spacing: 0;
  line-height: 19.9px;
}

.travel-agency .value-2 {
  width: 507px;
  height: 65px;
  top: 224px;
  position: absolute;
  left: 0;
}

.travel-agency .taxi-wrapper {
  position: absolute;
  width: 47px;
  height: 48px;
  top: 4px;
  left: 0;
  background-color: var(--x-5);
  border-radius: 13px;
}

.travel-agency .taxi {
  position: absolute;
  width: 22px;
  height: 19px;
  top: 16px;
  left: 13px;
}

.travel-agency .pay-the-first {
  position: absolute;
  top: 25px;
  left: 68px;
  font-family: "Poppins", Helvetica;
  font-weight: 400;
  color: var(--text-clr);
  font-size: 16px;
  letter-spacing: 0;
  line-height: 19.9px;
}

.travel-agency .value-3 {
  width: 561px;
  height: 84px;
  top: 112px;
  position: absolute;
  left: 0;
}

.travel-agency .water-sport-wrapper {
  position: absolute;
  width: 47px;
  height: 48px;
  top: 4px;
  left: 0;
  background-color: var(--x-4);
  border-radius: 13px;
}

.travel-agency .water-sport {
  position: absolute;
  width: 22px;
  height: 18px;
  top: 14px;
  left: 13px;
}

.travel-agency .destinations {
  position: absolute;
  width: 1052px;
  height: 3235px;
  top: 1567px;
  left: 42px;
}

.travel-agency .destination {
  position: absolute;
  width: 321px;
  height: 457px;
  top: 160px;
  left: 725px;
}

.travel-agency .overlap-5 {
  position: relative;
  width: 474px;
  height: 638px;
  top: -1px;
  left: -79px;
  background-image: url(./img/rectangle-14-4.png);
  background-size: cover;
  background-position: 50% 50%;
}

.travel-agency .card {
  position: absolute;
  width: 319px;
  height: 130px;
  top: 328px;
  left: 79px;
}

.travel-agency .overlap-group-3 {
  position: relative;
  width: 315px;
  height: 130px;
  background-color: #ffffff;
  border-radius: 0px 0px 24px 24px;
}

.travel-agency .text-wrapper-11 {
  position: absolute;
  top: 18px;
  left: 20px;
  font-family: "Poppins", Helvetica;
  font-weight: 600;
  color: var(--text-clr);
  font-size: 18px;
  letter-spacing: 0;
  line-height: 22.4px;
  white-space: nowrap;
}

.travel-agency .text-wrapper-12 {
  position: absolute;
  top: 18px;
  left: 240px;
  font-family: "Open Sans", Helvetica;
  font-weight: 700;
  color: var(--text-clr);
  font-size: 18px;
  letter-spacing: 0;
  line-height: 22.4px;
  white-space: nowrap;
}

.travel-agency .text-wrapper-13 {
  position: absolute;
  top: 407px;
  left: 319px;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
  color: var(--text-clr);
  font-size: 14px;
  letter-spacing: 0;
  line-height: 17.4px;
  white-space: nowrap;
}

.travel-agency .text-wrapper-14 {
  position: absolute;
  top: 390px;
  left: 99px;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
  color: var(--text-clr);
  font-size: 14px;
  letter-spacing: 0;
  line-height: 17.4px;
  white-space: nowrap;
}

.travel-agency .text-wrapper-15 {
  top: 409px;
  left: 99px;
  color: #c4c4c4;
  font-size: 12px;
  letter-spacing: 0;
  line-height: 12.8px;
  white-space: nowrap;
  position: absolute;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
}

.travel-agency .destination-2 {
  width: 315px;
  left: 0;
  position: absolute;
  height: 457px;
  top: 160px;
}

.travel-agency .card-wrapper {
  position: relative;
  width: 474px;
  height: 638px;
  top: -1px;
  left: -79px;
  background-image: url(./img/rectangle-14-2.png);
  background-size: cover;
  background-position: 50% 50%;
}

.travel-agency .div-wrapper {
  position: relative;
  width: 325px;
  height: 130px;
  top: 328px;
  left: 79px;
}

.travel-agency .text-wrapper-16 {
  position: absolute;
  top: 17px;
  left: 20px;
  font-family: "Poppins", Helvetica;
  font-weight: 600;
  color: var(--text-clr);
  font-size: 18px;
  letter-spacing: 0;
  line-height: 22.4px;
  white-space: nowrap;
}

.travel-agency .text-wrapper-17 {
  top: 79px;
  left: 234px;
  color: var(--text-clr);
  font-size: 14px;
  letter-spacing: 0;
  line-height: 17.4px;
  white-space: nowrap;
  position: absolute;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
}

.travel-agency .text-wrapper-18 {
  top: 62px;
  left: 20px;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
  color: var(--text-clr);
  font-size: 14px;
  line-height: 17.4px;
  white-space: nowrap;
  position: absolute;
  letter-spacing: 0;
}

.travel-agency .element-adults-child {
  top: 81px;
  left: 20px;
  color: #c4c4c4;
  font-size: 12px;
  letter-spacing: 0;
  line-height: 12.8px;
  white-space: nowrap;
  position: absolute;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
}

.travel-agency .text-wrapper-19 {
  position: absolute;
  top: 18px;
  left: 230px;
  font-family: "Open Sans", Helvetica;
  font-weight: 700;
  color: var(--text-clr);
  font-size: 18px;
  letter-spacing: 0;
  line-height: 22.4px;
  white-space: nowrap;
}

.travel-agency .destination-3 {
  width: 321px;
  left: 350px;
  position: absolute;
  height: 457px;
  top: 160px;
}

.travel-agency .overlap-6 {
  position: relative;
  width: 474px;
  height: 638px;
  top: -1px;
  left: -79px;
  background-image: url(./img/rectangle-14.png);
  background-size: cover;
  background-position: 50% 50%;
}

.travel-agency .subheading-2 {
  position: absolute;
  top: 0;
  left: 459px;
  font-family: "Poppins", Helvetica;
  font-weight: 600;
  color: var(--text-clr);
  font-size: 18px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .heading-2 {
  top: 35px;
  left: 317px;
  font-family: "Volkhov", Helvetica;
  color: #14183e;
  font-size: 50px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
  position: absolute;
  font-weight: 700;
}

.travel-agency .heading-3 {
  top: 1362px;
  left: 287px;
  font-family: "Volkhov", Helvetica;
  color: #14183e;
  font-size: 50px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
  position: absolute;
  font-weight: 700;
}

.travel-agency .heading-4 {
  top: 2308px;
  left: 381px;
  font-family: "Volkhov", Helvetica;
  color: #14183e;
  font-size: 50px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
  position: absolute;
  font-weight: 700;
}

.travel-agency .heading-5 {
  top: 3170px;
  left: 375px;
  font-family: "Volkhov", Helvetica;
  color: #14183e;
  font-size: 50px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
  position: absolute;
  font-weight: 700;
}

.travel-agency .heading-6 {
  top: 3033px;
  left: 493px;
  font-family: "Inter", Helvetica;
  color: #3dbb74;
  font-size: 22px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
  position: absolute;
  font-weight: 700;
}

.travel-agency .services {
  position: absolute;
  width: 1237px;
  height: 525px;
  top: 919px;
  left: 18px;
}

.travel-agency .group-3 {
  position: absolute;
  width: 197px;
  height: 166px;
  top: 0;
  left: 1080px;
}

.travel-agency .text-wrapper-20 {
  position: absolute;
  top: 0;
  left: 0;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--yellow);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-21 {
  position: absolute;
  top: 35px;
  left: 0;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-22 {
  position: absolute;
  top: 70px;
  left: 0;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-23 {
  position: absolute;
  top: 0;
  left: 35px;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-24 {
  position: absolute;
  top: 35px;
  left: 35px;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-25 {
  position: absolute;
  top: 70px;
  left: 35px;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-26 {
  position: absolute;
  top: 105px;
  left: 35px;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-27 {
  position: absolute;
  top: 0;
  left: 70px;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-28 {
  position: absolute;
  top: 35px;
  left: 70px;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-29 {
  position: absolute;
  top: 70px;
  left: 70px;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-2nd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-30 {
  position: absolute;
  top: 105px;
  left: 70px;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-31 {
  position: absolute;
  top: 140px;
  left: 70px;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-32 {
  position: absolute;
  top: 0;
  left: 105px;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-33 {
  position: absolute;
  top: 35px;
  left: 105px;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-34 {
  position: absolute;
  top: 70px;
  left: 105px;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-35 {
  position: absolute;
  top: 105px;
  left: 105px;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-36 {
  position: absolute;
  top: 140px;
  left: 105px;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-37 {
  position: absolute;
  top: 0;
  left: 140px;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-38 {
  position: absolute;
  top: 35px;
  left: 140px;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-39 {
  position: absolute;
  top: 70px;
  left: 140px;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-40 {
  position: absolute;
  top: 105px;
  left: 140px;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-41 {
  position: absolute;
  top: 140px;
  left: 140px;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-42 {
  position: absolute;
  top: 8px;
  left: 503px;
  font-family: "Poppins", Helvetica;
  font-weight: 600;
  color: var(--text-clr);
  font-size: 18px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .overlap-7 {
  position: absolute;
  width: 878px;
  height: 480px;
  top: 45px;
  left: 0;
}

.travel-agency .overlap-8 {
  position: absolute;
  width: 878px;
  height: 480px;
  top: 0;
  left: 0;
}

.travel-agency .why-family-EMI {
  position: absolute;
  top: 0;
  left: 226px;
  font-family: "Volkhov", Helvetica;
  font-weight: 700;
  color: #14183e;
  font-size: 50px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .category-active {
  position: absolute;
  width: 306px;
  height: 347px;
  top: 133px;
  left: 233px;
}

.travel-agency .overlap-9 {
  position: relative;
  width: 302px;
  height: 347px;
}

.travel-agency .rectangle-4 {
  position: absolute;
  width: 100px;
  height: 100px;
  top: 247px;
  left: 0;
  background-color: #df6951;
  border-radius: 30px 0px 10px 0px;
}

.travel-agency .rectangle-5 {
  position: absolute;
  width: 267px;
  height: 314px;
  top: 0;
  left: 35px;
  background-color: var(--white);
  border-radius: 36px;
  box-shadow: 0px 1.85px 3.15px #00000001, 0px 8.15px 6.52px #00000002, 0px 20px 13px #00000003,
    0px 38.52px 25.48px #00000003, 0px 64.81px 46.85px #00000004, 0px 100px 80px #00000005;
}

.travel-agency .swap-plans-days {
  position: absolute;
  width: 181px;
  top: 191px;
  left: 79px;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
  color: var(--text-clr);
  font-size: 16px;
  text-align: center;
  letter-spacing: 0;
  line-height: 26px;
}

.travel-agency .text-wrapper-43 {
  position: absolute;
  top: 149px;
  left: 109px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 20px;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .group-4 {
  position: absolute;
  width: 86px;
  height: 66px;
  top: 40px;
  left: 132px;
}

.travel-agency .overlap-group-4 {
  position: relative;
  height: 66px;
}

.travel-agency .rectangle-6 {
  position: absolute;
  width: 50px;
  height: 49px;
  top: 0;
  left: 0;
  background-color: #fff1da;
  border-radius: 18px 5px 10px 5px;
}

.travel-agency .vector {
  position: absolute;
  width: 60px;
  height: 60px;
  top: 6px;
  left: 25px;
}

.travel-agency .category {
  position: absolute;
  width: 405px;
  height: 378px;
  top: 47px;
  left: 0;
}

.travel-agency .overlap-10 {
  position: relative;
  width: 401px;
  height: 378px;
}

.travel-agency .noun-time {
  position: absolute;
  width: 302px;
  height: 378px;
  top: 0;
  left: 99px;
}

.travel-agency .group-5 {
  position: absolute;
  width: 79px;
  height: 74px;
  top: 132px;
  left: 71px;
}

.travel-agency .overlap-group-5 {
  position: relative;
  height: 74px;
}

.travel-agency .rectangle-7 {
  position: absolute;
  width: 50px;
  height: 49px;
  top: 25px;
  left: 29px;
  background-color: #fff1da;
  border-radius: 18px 5px 10px 5px;
  transform: rotate(-180deg);
}

.travel-agency .vector-2 {
  position: absolute;
  width: 58px;
  height: 58px;
  top: 0;
  left: 0;
}

.travel-agency .text-wrapper-44 {
  position: absolute;
  width: 181px;
  top: 277px;
  left: 10px;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
  color: var(--text-clr);
  font-size: 16px;
  text-align: center;
  letter-spacing: 0;
  line-height: 26px;
}

.travel-agency .text-wrapper-45 {
  position: absolute;
  top: 235px;
  left: 0;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 20px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .category-2 {
  position: absolute;
  width: 231px;
  height: 229px;
  top: 173px;
  left: 590px;
}

.travel-agency .group-6 {
  position: absolute;
  width: 73px;
  height: 67px;
  top: 0;
  left: 107px;
}

.travel-agency .overlap-group-6 {
  position: relative;
  height: 67px;
}

.travel-agency .rectangle-8 {
  position: absolute;
  width: 50px;
  height: 49px;
  top: 0;
  left: 0;
  background-color: #fff1da;
  border-radius: 18px 5px 10px 5px;
  transform: rotate(180deg);
}

.travel-agency .vector-3 {
  position: absolute;
  width: 48px;
  height: 61px;
  top: 6px;
  left: 25px;
}

.travel-agency .text-wrapper-46 {
  position: absolute;
  width: 181px;
  top: 151px;
  left: 24px;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
  color: var(--text-clr);
  font-size: 16px;
  text-align: center;
  letter-spacing: 0;
  line-height: 26px;
}

.travel-agency .text-wrapper-47 {
  position: absolute;
  top: 109px;
  left: 0;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 20px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .category-3 {
  position: absolute;
  width: 201px;
  height: 223px;
  top: 224px;
  left: 905px;
}

.travel-agency .group-7 {
  position: absolute;
  width: 77px;
  height: 73px;
  top: 0;
  left: 98px;
}

.travel-agency .overlap-group-7 {
  position: relative;
  height: 73px;
}

.travel-agency .rectangle-9 {
  position: absolute;
  width: 50px;
  height: 49px;
  top: 24px;
  left: 0;
  background-color: #fff1da;
  border-radius: 18px 5px 10px 5px;
}

.travel-agency .vector-4 {
  position: absolute;
  width: 55px;
  height: 55px;
  top: 0;
  left: 22px;
}

.travel-agency .text-wrapper-48 {
  position: absolute;
  width: 181px;
  top: 145px;
  left: 8px;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
  color: var(--text-clr);
  font-size: 16px;
  text-align: center;
  letter-spacing: 0;
  line-height: 26px;
}

.travel-agency .text-wrapper-49 {
  position: absolute;
  top: 103px;
  left: 0;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 20px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .hero {
  position: absolute;
  width: 1270px;
  height: 4940px;
  top: 0;
  left: 10px;
}

.travel-agency .overlap-11 {
  position: relative;
  height: 4940px;
}

.travel-agency .top-nav {
  position: absolute;
  width: 1270px;
  height: 55px;
  top: 0;
  left: 0;
}

.travel-agency .hero-content {
  position: absolute;
  width: 1201px;
  height: 4924px;
  top: 16px;
  left: 7px;
}

.travel-agency .overlap-12 {
  position: relative;
  height: 4924px;
}

.travel-agency .desc {
  position: absolute;
  width: 696px;
  height: 4770px;
  top: 154px;
  left: 0;
}

.travel-agency .CTA {
  position: absolute;
  width: 649px;
  height: 4295px;
  top: 475px;
  left: 0;
}

.travel-agency .CTA-2 {
  top: -15px;
  left: -35px;
  position: absolute;
  width: 240px;
  height: 130px;
}

.travel-agency .CTA-3 {
  top: 4220px;
  left: 444px;
  position: absolute;
  width: 240px;
  height: 130px;
}

.travel-agency .tagline {
  position: absolute;
  top: 0;
  left: 2px;
  font-family: "Poppins", Helvetica;
  font-weight: 700;
  color: #df6951;
  font-size: 20px;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .desc-2 {
  position: absolute;
  width: 477px;
  top: 351px;
  left: 0;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
  color: #5e6282;
  font-size: 16px;
  letter-spacing: 0;
  line-height: 30px;
}

.travel-agency .overlap-group-8 {
  position: absolute;
  width: 688px;
  height: 178px;
  top: 54px;
  left: 2px;
}

.travel-agency .decore-2 {
  position: absolute;
  width: 385px;
  height: 12px;
  top: 69px;
  left: 303px;
}

.travel-agency .heading-7 {
  top: 0;
  left: 0;
  font-family: "Volkhov", Helvetica;
  color: var(--x-1st);
  font-size: 84px;
  letter-spacing: -3.36px;
  line-height: 89px;
  position: absolute;
  font-weight: 700;
}

.travel-agency .image-2 {
  position: absolute;
  width: 692px;
  height: 785px;
  top: 0;
  left: 509px;
}

.travel-agency .overlap-13 {
  position: relative;
  height: 785px;
}

.travel-agency .plane {
  position: absolute;
  width: 167px;
  height: 153px;
  top: 145px;
  left: 525px;
}

.travel-agency .family-image {
  position: absolute;
  width: 595px;
  height: 785px;
  top: 0;
  left: 0;
  background-image: url(./img/family-holiday-1-1.png);
  background-size: 100% 100%;
}

.travel-agency .plane-2 {
  position: absolute;
  width: 167px;
  height: 153px;
  top: 98px;
  left: 41px;
}

.travel-agency .GOLD-CARD {
  position: absolute;
  width: 351px;
  height: 743px;
  top: 3040px;
  left: 402px;
}

.travel-agency .SILVER-CARD {
  position: absolute;
  width: 351px;
  height: 743px;
  top: 3033px;
  left: 22px;
}

.travel-agency .PLATINUM {
  position: absolute;
  width: 351px;
  height: 743px;
  top: 3046px;
  left: 782px;
}

.travel-agency .text-wrapper-50 {
  position: absolute;
  top: 4010px;
  left: 77px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 26px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .text-wrapper-51 {
  position: absolute;
  top: 4008px;
  left: 474px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 26px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .text-wrapper-52 {
  position: absolute;
  top: 4006px;
  left: 871px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 26px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .text-wrapper-53 {
  top: 4110px;
  left: 77px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 26px;
  text-align: center;
  line-height: normal;
  position: absolute;
  letter-spacing: 0;
}

.travel-agency .text-wrapper-54 {
  top: 4108px;
  left: 474px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 26px;
  text-align: center;
  line-height: normal;
  position: absolute;
  letter-spacing: 0;
}

.travel-agency .text-wrapper-55 {
  top: 4108px;
  left: 871px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 26px;
  text-align: center;
  line-height: normal;
  position: absolute;
  letter-spacing: 0;
}

.travel-agency .text-wrapper-56 {
  top: 4233px;
  left: 77px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 26px;
  text-align: center;
  line-height: normal;
  position: absolute;
  letter-spacing: 0;
}

.travel-agency .text-wrapper-57 {
  position: absolute;
  top: 4231px;
  left: 474px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 26px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .text-wrapper-58 {
  top: 4231px;
  left: 871px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 26px;
  text-align: center;
  line-height: normal;
  position: absolute;
  letter-spacing: 0;
}

.travel-agency .text-wrapper-59 {
  position: absolute;
  top: 4356px;
  left: 81px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 26px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .text-wrapper-60 {
  position: absolute;
  top: 4354px;
  left: 474px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 26px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .text-wrapper-61 {
  position: absolute;
  top: 4354px;
  left: 875px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 26px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .text-wrapper-62 {
  position: absolute;
  top: 4479px;
  left: 81px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 26px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .to-view-all-of-our {
  position: absolute;
  top: 4814px;
  left: 414px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 26px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .text-wrapper-63 {
  position: absolute;
  top: 4479px;
  left: 478px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 26px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .text-wrapper-64 {
  position: absolute;
  top: 4477px;
  left: 875px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 26px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .element-adults-infant {
  top: 4055px;
  left: 79px;
  color: #3dbb74;
  font-size: 16px;
  text-align: center;
  letter-spacing: 0;
  line-height: 12.8px;
  white-space: nowrap;
  position: absolute;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
}

.travel-agency .element-adults-infant-2 {
  top: 4053px;
  left: 476px;
  color: #3dbb74;
  font-size: 16px;
  text-align: center;
  letter-spacing: 0;
  line-height: 12.8px;
  white-space: nowrap;
  position: absolute;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
}

.travel-agency .flexcontainer {
  width: 267px;
  top: 4053px;
  left: 876px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 6px;
  position: absolute;
  height: 51px;
}

.travel-agency .text {
  position: relative;
  align-self: stretch;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
  color: #3dbb74;
  font-size: 16px;
  letter-spacing: 0;
  line-height: 12.8px;
}

.travel-agency .text-wrapper-65 {
  font-family: "Poppins", Helvetica;
  font-weight: 500;
  color: #3dbb74;
  font-size: 16px;
  letter-spacing: 0;
  line-height: 12.8px;
}

.travel-agency .flexcontainer-2 {
  width: 267px;
  top: 4153px;
  left: 876px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 6px;
  position: absolute;
  height: 51px;
}

.travel-agency .flexcontainer-3 {
  width: 297px;
  top: 4276px;
  left: 876px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 6px;
  position: absolute;
  height: 51px;
}

.travel-agency .flexcontainer-4 {
  width: 282px;
  top: 4399px;
  left: 875px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 6px;
  position: absolute;
  height: 51px;
}

.travel-agency .flexcontainer-5 {
  width: 254px;
  top: 4528px;
  left: 876px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 6px;
  position: absolute;
  height: 51px;
}

.travel-agency .element-adults-child-2 {
  top: 4155px;
  left: 81px;
  color: #3dbb74;
  font-size: 16px;
  text-align: center;
  letter-spacing: 0;
  line-height: 12.8px;
  white-space: nowrap;
  position: absolute;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
}

.travel-agency .element-adults-children-2 {
  top: 4153px;
  left: 476px;
  color: #3dbb74;
  font-size: 16px;
  text-align: center;
  letter-spacing: 0;
  line-height: 12.8px;
  white-space: nowrap;
  position: absolute;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
}

.travel-agency .element-adults-children-3 {
  top: 4278px;
  left: 79px;
  color: #3dbb74;
  font-size: 16px;
  text-align: center;
  letter-spacing: 0;
  line-height: 12.8px;
  white-space: nowrap;
  position: absolute;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
}

.travel-agency .element-adults-children-4 {
  top: 4276px;
  left: 478px;
  color: #3dbb74;
  font-size: 16px;
  text-align: center;
  letter-spacing: 0;
  line-height: 12.8px;
  white-space: nowrap;
  position: absolute;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
}

.travel-agency .element-adults-children-5 {
  top: 4401px;
  left: 81px;
  color: #3dbb74;
  font-size: 16px;
  text-align: center;
  letter-spacing: 0;
  line-height: 12.8px;
  white-space: nowrap;
  position: absolute;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
}

.travel-agency .element-adult-children {
  top: 4399px;
  left: 476px;
  color: #3dbb74;
  font-size: 16px;
  text-align: center;
  letter-spacing: 0;
  line-height: 12.8px;
  white-space: nowrap;
  position: absolute;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
}

.travel-agency .element-adult-child {
  top: 4524px;
  left: 81px;
  color: #3dbb74;
  font-size: 16px;
  text-align: center;
  letter-spacing: 0;
  line-height: 12.8px;
  position: absolute;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
}

.travel-agency .element-adults-teenager {
  top: 4521px;
  left: 476px;
  color: #3dbb74;
  font-size: 16px;
  text-align: center;
  letter-spacing: 0;
  line-height: 12.8px;
  white-space: nowrap;
  position: absolute;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
}

.travel-agency .TRIPMILESTONE-2 {
  position: absolute;
  top: 0;
  left: 0;
  font-family: "Poppins", Helvetica;
  font-weight: 800;
  color: transparent;
  font-size: 26px;
  letter-spacing: 1.56px;
  line-height: normal;
}

.travel-agency .rectangle-10 {
  position: absolute;
  width: 166px;
  height: 51px;
  top: 4589px;
  left: 517px;
  border-radius: 26px;
  border: 3px solid;
  border-color: #3dbb74;
}
