.travel-agency {
  position: relative;
  width: 1440px;
  height: 5445px;
  background-color: #ffffff;
  overflow: hidden;
}

.travel-agency .footer {
  position: absolute;
  width: 1628px;
  height: 634px;
  top: 5077px;
  left: 184px;
  background-color: transparent;
}

.travel-agency .overlap-group {
  position: absolute;
  width: 750px;
  height: 514px;
  top: 120px;
  left: 870px;
}

.travel-agency .decore {
  position: absolute;
  width: 479px;
  height: 497px;
  top: 17px;
  left: 271px;
  background-color: var(--d-2);
  border-radius: 239.47px/248.43px;
  transform: rotate(180deg);
  filter: blur(150px);
  opacity: 0.4;
}

.travel-agency .najam-centre {
  position: absolute;
  top: 0;
  left: 0;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
  color: #5e6282;
  font-size: 14px;
  letter-spacing: 0;
  line-height: 21px;
}

.travel-agency .outbound {
  position: absolute;
  width: 111px;
  height: 71px;
  top: 21px;
  left: 850px;
}

.travel-agency .social {
  position: absolute;
  width: 131px;
  height: 65px;
  top: -8px;
  left: -10px;
}

.travel-agency .overlap {
  position: absolute;
  width: 257px;
  height: 117px;
  top: 0;
  left: 0;
}

.travel-agency .company-desc {
  position: absolute;
  width: 255px;
  height: 117px;
  top: 0;
  left: 2px;
}

.travel-agency .group {
  position: absolute;
  width: 183px;
  height: 53px;
  top: 0;
  left: 0;
}

.travel-agency .rectangle {
  position: absolute;
  width: 84px;
  height: 12px;
  top: 41px;
  left: 89px;
  background-color: #fff1da;
  border-radius: 7px;
}

.travel-agency .div {
  position: absolute;
  width: 20px;
  height: 12px;
  top: 0;
  left: 163px;
  background-color: #fff1da;
  border-radius: 7px;
}

.travel-agency .rectangle-2 {
  position: absolute;
  width: 20px;
  height: 12px;
  top: 41px;
  left: 0;
  background-color: #fff1da;
  border-radius: 7px;
}

.travel-agency .make-your-dream {
  position: absolute;
  top: 85px;
  left: 0;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
  color: var(--text-clr);
  font-size: 13px;
  letter-spacing: 0;
  line-height: 16.2px;
}

.travel-agency .TRIPMILESTONE {
  position: absolute;
  top: 25px;
  left: 0;
  font-family: "Poppins", Helvetica;
  font-weight: 800;
  color: transparent;
  font-size: 26px;
  letter-spacing: 1.56px;
  line-height: normal;
}

.travel-agency .text-wrapper {
  color: #3dbb74;
}

.travel-agency .span {
  color: #181e4b;
}

.travel-agency .nav-columns {
  width: 534px;
  top: 21px;
  left: 292px;
  position: absolute;
  height: 82px;
}

.travel-agency .nav-row {
  width: 112px;
  top: 0;
  left: 0;
  position: absolute;
  height: 82px;
}

.travel-agency .text-wrapper-2 {
  position: absolute;
  top: 0;
  left: 0;
  font-family: "Poppins", Helvetica;
  font-weight: 700;
  color: var(--black);
  font-size: 21px;
  letter-spacing: 0;
  line-height: 26.1px;
  white-space: nowrap;
}

.travel-agency .text-wrapper-3 {
  position: absolute;
  top: 60px;
  left: 0;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
  color: var(--text-clr);
  font-size: 18px;
  letter-spacing: 0;
  line-height: 22.4px;
  white-space: nowrap;
}

.travel-agency .nav-row-2 {
  width: 93px;
  height: 80px;
  left: 180px;
  position: absolute;
  top: 0;
}

.travel-agency .text-wrapper-4 {
  position: absolute;
  top: 0;
  left: 1px;
  font-family: "Poppins", Helvetica;
  font-weight: 700;
  color: var(--black);
  font-size: 21px;
  letter-spacing: 0;
  line-height: 26.1px;
  white-space: nowrap;
}

.travel-agency .text-wrapper-5 {
  position: absolute;
  top: 58px;
  left: 0;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
  color: var(--text-clr);
  font-size: 18px;
  letter-spacing: 0;
  line-height: 22.4px;
  white-space: nowrap;
}

.travel-agency .nav-row-3 {
  width: 178px;
  height: 82px;
  left: 360px;
  position: absolute;
  top: 0;
}

.travel-agency .copyrights {
  position: absolute;
  top: 258px;
  left: 432px;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
  color: var(--text-clr);
  font-size: 14px;
  letter-spacing: 0;
  line-height: 17.4px;
  white-space: nowrap;
}

.travel-agency .text-wrapper-6 {
  position: absolute;
  top: 95px;
  left: 870px;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
  color: #5e6282;
  font-size: 14px;
  letter-spacing: 0;
  line-height: 21px;
  white-space: nowrap;
}

.travel-agency .overlap-2 {
  position: absolute;
  width: 1280px;
  height: 4940px;
  top: 47px;
  left: 131px;
}

.travel-agency .book-a-trip {
  position: absolute;
  width: 1047px;
  height: 492px;
  top: 2370px;
  left: 55px;
}

.travel-agency .heading {
  position: absolute;
  top: 42px;
  left: 0;
  font-family: "Volkhov", Helvetica;
  font-weight: 700;
  color: #14183e;
  font-size: 50px;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .image {
  position: absolute;
  width: 414px;
  height: 459px;
  top: 19px;
  left: 629px;
}

.travel-agency .overlap-3 {
  position: relative;
  height: 459px;
}

.travel-agency .group-2 {
  position: absolute;
  width: 354px;
  height: 367px;
  top: 0;
  left: 60px;
  background-color: #59b1e6cc;
  border-radius: 177px/183.5px;
  filter: blur(150px);
  opacity: 0.4;
}

.travel-agency .overlap-wrapper {
  position: absolute;
  width: 370px;
  height: 400px;
  top: 59px;
  left: 0;
}

.travel-agency .overlap-4 {
  position: relative;
  width: 378px;
  height: 400px;
}

.travel-agency .overlap-group-wrapper {
  position: absolute;
  width: 378px;
  height: 400px;
  top: 0;
  left: 0;
}

.travel-agency .overlap-group-2 {
  position: relative;
  width: 370px;
  height: 400px;
  background-color: #ffffff;
  border-radius: 26px;
  box-shadow: 0px 1.85px 3.15px #00000001, 0px 8.15px 6.52px #00000002, 0px 20px 13px #00000003,
    0px 38.52px 25.48px #00000003, 0px 64.81px 46.85px #00000004, 0px 100px 80px #00000005;
}

.travel-agency .OPTIONS {
  position: absolute;
  width: 139px;
  height: 36px;
  top: 283px;
  left: 28px;
}

.travel-agency .LEAF {
  position: absolute;
  width: 36px;
  height: 36px;
  top: 0;
  left: 0;
  background-color: var(--circle);
  border-radius: 18px;
}

.travel-agency .img {
  position: absolute;
  width: 14px;
  height: 14px;
  top: 12px;
  left: 11px;
}

.travel-agency .send {
  position: absolute;
  width: 36px;
  height: 36px;
  top: 0;
  left: 103px;
  background-color: var(--circle);
  border-radius: 18px;
}

.travel-agency .map-icon {
  position: absolute;
  width: 36px;
  height: 36px;
  top: 0;
  left: 54px;
  background-color: var(--circle);
  border-radius: 18px;
}

.travel-agency .map {
  position: absolute;
  width: 14px;
  height: 14px;
  top: 11px;
  left: 10px;
}

.travel-agency .building {
  position: absolute;
  width: 16px;
  height: 16px;
  top: 348px;
  left: 25px;
}

.travel-agency .text-wrapper-7 {
  position: absolute;
  top: 349px;
  left: 56px;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
  color: var(--text-2);
  font-size: 16px;
  letter-spacing: -0.32px;
  line-height: 19.9px;
  white-space: nowrap;
}

.travel-agency .text-wrapper-8 {
  position: absolute;
  top: 243px;
  left: 25px;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
  color: var(--text-clr);
  font-size: 14px;
  letter-spacing: 0;
  line-height: 17.4px;
  white-space: nowrap;
}

.travel-agency .element-adults-children {
  top: 242px;
  left: 124px;
  color: var(--text-2);
  font-size: 16px;
  letter-spacing: -0.08px;
  line-height: 19.9px;
  white-space: nowrap;
  position: absolute;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
}

.travel-agency .rectangle-3 {
  position: absolute;
  width: 321px;
  height: 161px;
  top: 20px;
  left: 25px;
}

.travel-agency .text-wrapper-9 {
  position: absolute;
  top: 207px;
  left: 25px;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
  color: var(--black);
  font-size: 18px;
  letter-spacing: 0.27px;
  line-height: 22.4px;
  white-space: nowrap;
}

.travel-agency .line {
  position: absolute;
  width: 1px;
  height: 16px;
  top: 243px;
  left: 116px;
  object-fit: cover;
}

.travel-agency .heart {
  position: absolute;
  width: 1px;
  height: 1px;
  top: 350px;
  left: 325px;
}

.travel-agency .subheading {
  position: absolute;
  top: 0;
  left: 3px;
  font-family: "Poppins", Helvetica;
  font-weight: 600;
  color: var(--text-clr);
  font-size: 18px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .values {
  position: absolute;
  width: 589px;
  height: 289px;
  top: 203px;
  left: 0;
}

.travel-agency .value {
  position: absolute;
  width: 593px;
  height: 84px;
  top: 0;
  left: 0;
}

.travel-agency .selection-wrapper {
  position: absolute;
  width: 47px;
  height: 48px;
  top: 4px;
  left: 0;
  background-color: var(--x-3);
  border-radius: 13px;
}

.travel-agency .selection {
  position: absolute;
  width: 22px;
  height: 22px;
  top: 13px;
  left: 13px;
}

.travel-agency .text-wrapper-10 {
  position: absolute;
  top: 0;
  left: 68px;
  font-family: "Poppins", Helvetica;
  font-weight: 700;
  color: var(--text-clr);
  font-size: 16px;
  letter-spacing: 0;
  line-height: 19.9px;
  white-space: nowrap;
}

.travel-agency .p {
  position: absolute;
  top: 24px;
  left: 68px;
  font-family: "Poppins", Helvetica;
  font-weight: 400;
  color: var(--text-clr);
  font-size: 16px;
  letter-spacing: 0;
  line-height: 19.9px;
}

.travel-agency .value-2 {
  width: 507px;
  height: 65px;
  top: 224px;
  position: absolute;
  left: 0;
}

.travel-agency .taxi-wrapper {
  position: absolute;
  width: 47px;
  height: 48px;
  top: 4px;
  left: 0;
  background-color: var(--x-5);
  border-radius: 13px;
}

.travel-agency .taxi {
  position: absolute;
  width: 22px;
  height: 19px;
  top: 16px;
  left: 13px;
}

.travel-agency .pay-the-first {
  position: absolute;
  top: 25px;
  left: 68px;
  font-family: "Poppins", Helvetica;
  font-weight: 400;
  color: var(--text-clr);
  font-size: 16px;
  letter-spacing: 0;
  line-height: 19.9px;
}

.travel-agency .value-3 {
  width: 561px;
  height: 84px;
  top: 112px;
  position: absolute;
  left: 0;
}

.travel-agency .water-sport-wrapper {
  position: absolute;
  width: 47px;
  height: 48px;
  top: 4px;
  left: 0;
  background-color: var(--x-4);
  border-radius: 13px;
}

.travel-agency .water-sport {
  position: absolute;
  width: 22px;
  height: 18px;
  top: 14px;
  left: 13px;
}

.travel-agency .destinations {
  position: absolute;
  width: 1052px;
  height: 3235px;
  top: 1567px;
  left: 42px;
}

.travel-agency .destination {
  position: absolute;
  width: 321px;
  height: 457px;
  top: 160px;
  left: 725px;
}

.travel-agency .overlap-5 {
  position: relative;
  width: 474px;
  height: 638px;
  top: -1px;
  left: -79px;
  background-image: url(./img/rectangle-14-4.png);
  background-size: cover;
  background-position: 50% 50%;
}

.travel-agency .card {
  position: absolute;
  width: 319px;
  height: 130px;
  top: 328px;
  left: 79px;
}

.travel-agency .overlap-group-3 {
  position: relative;
  width: 315px;
  height: 130px;
  background-color: #ffffff;
  border-radius: 0px 0px 24px 24px;
}

.travel-agency .text-wrapper-11 {
  position: absolute;
  top: 18px;
  left: 20px;
  font-family: "Poppins", Helvetica;
  font-weight: 600;
  color: var(--text-clr);
  font-size: 18px;
  letter-spacing: 0;
  line-height: 22.4px;
  white-space: nowrap;
}

.travel-agency .text-wrapper-12 {
  position: absolute;
  top: 18px;
  left: 240px;
  font-family: "Open Sans", Helvetica;
  font-weight: 700;
  color: var(--text-clr);
  font-size: 18px;
  letter-spacing: 0;
  line-height: 22.4px;
  white-space: nowrap;
}

.travel-agency .text-wrapper-13 {
  position: absolute;
  top: 407px;
  left: 319px;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
  color: var(--text-clr);
  font-size: 14px;
  letter-spacing: 0;
  line-height: 17.4px;
  white-space: nowrap;
}

.travel-agency .text-wrapper-14 {
  position: absolute;
  top: 390px;
  left: 99px;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
  color: var(--text-clr);
  font-size: 14px;
  letter-spacing: 0;
  line-height: 17.4px;
  white-space: nowrap;
}

.travel-agency .text-wrapper-15 {
  top: 409px;
  left: 99px;
  color: #c4c4c4;
  font-size: 12px;
  letter-spacing: 0;
  line-height: 12.8px;
  white-space: nowrap;
  position: absolute;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
}

.travel-agency .destination-2 {
  width: 315px;
  left: 0;
  position: absolute;
  height: 457px;
  top: 160px;
}

.travel-agency .card-wrapper {
  position: relative;
  width: 474px;
  height: 638px;
  top: -1px;
  left: -79px;
  background-image: url(./img/rectangle-14-2.png);
  background-size: cover;
  background-position: 50% 50%;
}

.travel-agency .div-wrapper {
  position: relative;
  width: 325px;
  height: 130px;
  top: 328px;
  left: 79px;
}

.travel-agency .text-wrapper-16 {
  position: absolute;
  top: 17px;
  left: 20px;
  font-family: "Poppins", Helvetica;
  font-weight: 600;
  color: var(--text-clr);
  font-size: 18px;
  letter-spacing: 0;
  line-height: 22.4px;
  white-space: nowrap;
}

.travel-agency .text-wrapper-17 {
  top: 79px;
  left: 234px;
  color: var(--text-clr);
  font-size: 14px;
  letter-spacing: 0;
  line-height: 17.4px;
  white-space: nowrap;
  position: absolute;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
}

.travel-agency .text-wrapper-18 {
  top: 62px;
  left: 20px;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
  color: var(--text-clr);
  font-size: 14px;
  line-height: 17.4px;
  white-space: nowrap;
  position: absolute;
  letter-spacing: 0;
}

.travel-agency .element-adults-child {
  top: 81px;
  left: 20px;
  color: #c4c4c4;
  font-size: 12px;
  letter-spacing: 0;
  line-height: 12.8px;
  white-space: nowrap;
  position: absolute;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
}

.travel-agency .text-wrapper-19 {
  position: absolute;
  top: 18px;
  left: 230px;
  font-family: "Open Sans", Helvetica;
  font-weight: 700;
  color: var(--text-clr);
  font-size: 18px;
  letter-spacing: 0;
  line-height: 22.4px;
  white-space: nowrap;
}

.travel-agency .destination-3 {
  width: 321px;
  left: 350px;
  position: absolute;
  height: 457px;
  top: 160px;
}

.travel-agency .overlap-6 {
  position: relative;
  width: 474px;
  height: 638px;
  top: -1px;
  left: -79px;
  background-image: url(./img/rectangle-14.png);
  background-size: cover;
  background-position: 50% 50%;
}

.travel-agency .subheading-2 {
  position: absolute;
  top: 0;
  left: 459px;
  font-family: "Poppins", Helvetica;
  font-weight: 600;
  color: var(--text-clr);
  font-size: 18px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .heading-2 {
  top: 35px;
  left: 317px;
  font-family: "Volkhov", Helvetica;
  color: #14183e;
  font-size: 50px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
  position: absolute;
  font-weight: 700;
}

.travel-agency .heading-3 {
  top: 1362px;
  left: 287px;
  font-family: "Volkhov", Helvetica;
  color: #14183e;
  font-size: 50px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
  position: absolute;
  font-weight: 700;
}

.travel-agency .heading-4 {
  top: 2308px;
  left: 381px;
  font-family: "Volkhov", Helvetica;
  color: #14183e;
  font-size: 50px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
  position: absolute;
  font-weight: 700;
}

.travel-agency .heading-5 {
  top: 3170px;
  left: 375px;
  font-family: "Volkhov", Helvetica;
  color: #14183e;
  font-size: 50px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
  position: absolute;
  font-weight: 700;
}

.travel-agency .heading-6 {
  top: 3033px;
  left: 493px;
  font-family: "Inter", Helvetica;
  color: #3dbb74;
  font-size: 22px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
  position: absolute;
  font-weight: 700;
}

.travel-agency .services {
  position: absolute;
  width: 1237px;
  height: 525px;
  top: 919px;
  left: 18px;
}

.travel-agency .group-3 {
  position: absolute;
  width: 197px;
  height: 166px;
  top: 0;
  left: 1080px;
}

.travel-agency .text-wrapper-20 {
  position: absolute;
  top: 0;
  left: 0;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--yellow);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-21 {
  position: absolute;
  top: 35px;
  left: 0;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-22 {
  position: absolute;
  top: 70px;
  left: 0;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-23 {
  position: absolute;
  top: 0;
  left: 35px;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-24 {
  position: absolute;
  top: 35px;
  left: 35px;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-25 {
  position: absolute;
  top: 70px;
  left: 35px;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-26 {
  position: absolute;
  top: 105px;
  left: 35px;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-27 {
  position: absolute;
  top: 0;
  left: 70px;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-28 {
  position: absolute;
  top: 35px;
  left: 70px;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-29 {
  position: absolute;
  top: 70px;
  left: 70px;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-2nd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-30 {
  position: absolute;
  top: 105px;
  left: 70px;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-31 {
  position: absolute;
  top: 140px;
  left: 70px;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-32 {
  position: absolute;
  top: 0;
  left: 105px;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-33 {
  position: absolute;
  top: 35px;
  left: 105px;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-34 {
  position: absolute;
  top: 70px;
  left: 105px;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-35 {
  position: absolute;
  top: 105px;
  left: 105px;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-36 {
  position: absolute;
  top: 140px;
  left: 105px;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-37 {
  position: absolute;
  top: 0;
  left: 140px;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-38 {
  position: absolute;
  top: 35px;
  left: 140px;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-39 {
  position: absolute;
  top: 70px;
  left: 140px;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-40 {
  position: absolute;
  top: 105px;
  left: 140px;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-41 {
  position: absolute;
  top: 140px;
  left: 140px;
  font-family: "Roboto", Helvetica;
  font-weight: 400;
  color: var(--x-3rd);
  font-size: 22px;
  letter-spacing: 0;
  line-height: normal;
  white-space: nowrap;
}

.travel-agency .text-wrapper-42 {
  position: absolute;
  top: 8px;
  left: 503px;
  font-family: "Poppins", Helvetica;
  font-weight: 600;
  color: var(--text-clr);
  font-size: 18px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .overlap-7 {
  position: absolute;
  width: 878px;
  height: 480px;
  top: 45px;
  left: 0;
}

.travel-agency .overlap-8 {
  position: absolute;
  width: 878px;
  height: 480px;
  top: 0;
  left: 0;
}

.travel-agency .why-family-EMI {
  position: absolute;
  top: 0;
  left: 226px;
  font-family: "Volkhov", Helvetica;
  font-weight: 700;
  color: #14183e;
  font-size: 50px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .category-active {
  position: absolute;
  width: 306px;
  height: 347px;
  top: 133px;
  left: 233px;
}

.travel-agency .overlap-9 {
  position: relative;
  width: 302px;
  height: 347px;
}

.travel-agency .rectangle-4 {
  position: absolute;
  width: 100px;
  height: 100px;
  top: 247px;
  left: 0;
  background-color: #df6951;
  border-radius: 30px 0px 10px 0px;
}

.travel-agency .rectangle-5 {
  position: absolute;
  width: 267px;
  height: 314px;
  top: 0;
  left: 35px;
  background-color: var(--white);
  border-radius: 36px;
  box-shadow: 0px 1.85px 3.15px #00000001, 0px 8.15px 6.52px #00000002, 0px 20px 13px #00000003,
    0px 38.52px 25.48px #00000003, 0px 64.81px 46.85px #00000004, 0px 100px 80px #00000005;
}

.travel-agency .swap-plans-days {
  position: absolute;
  width: 181px;
  top: 191px;
  left: 79px;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
  color: var(--text-clr);
  font-size: 16px;
  text-align: center;
  letter-spacing: 0;
  line-height: 26px;
}

.travel-agency .text-wrapper-43 {
  position: absolute;
  top: 149px;
  left: 109px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 20px;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .group-4 {
  position: absolute;
  width: 86px;
  height: 66px;
  top: 40px;
  left: 132px;
}

.travel-agency .overlap-group-4 {
  position: relative;
  height: 66px;
}

.travel-agency .rectangle-6 {
  position: absolute;
  width: 50px;
  height: 49px;
  top: 0;
  left: 0;
  background-color: #fff1da;
  border-radius: 18px 5px 10px 5px;
}

.travel-agency .vector {
  position: absolute;
  width: 60px;
  height: 60px;
  top: 6px;
  left: 25px;
}

.travel-agency .category {
  position: absolute;
  width: 405px;
  height: 378px;
  top: 47px;
  left: 0;
}

.travel-agency .overlap-10 {
  position: relative;
  width: 401px;
  height: 378px;
}

.travel-agency .noun-time {
  position: absolute;
  width: 302px;
  height: 378px;
  top: 0;
  left: 99px;
}

.travel-agency .group-5 {
  position: absolute;
  width: 79px;
  height: 74px;
  top: 132px;
  left: 71px;
}

.travel-agency .overlap-group-5 {
  position: relative;
  height: 74px;
}

.travel-agency .rectangle-7 {
  position: absolute;
  width: 50px;
  height: 49px;
  top: 25px;
  left: 29px;
  background-color: #fff1da;
  border-radius: 18px 5px 10px 5px;
  transform: rotate(-180deg);
}

.travel-agency .vector-2 {
  position: absolute;
  width: 58px;
  height: 58px;
  top: 0;
  left: 0;
}

.travel-agency .text-wrapper-44 {
  position: absolute;
  width: 181px;
  top: 277px;
  left: 10px;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
  color: var(--text-clr);
  font-size: 16px;
  text-align: center;
  letter-spacing: 0;
  line-height: 26px;
}

.travel-agency .text-wrapper-45 {
  position: absolute;
  top: 235px;
  left: 0;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 20px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .category-2 {
  position: absolute;
  width: 231px;
  height: 229px;
  top: 173px;
  left: 590px;
}

.travel-agency .group-6 {
  position: absolute;
  width: 73px;
  height: 67px;
  top: 0;
  left: 107px;
}

.travel-agency .overlap-group-6 {
  position: relative;
  height: 67px;
}

.travel-agency .rectangle-8 {
  position: absolute;
  width: 50px;
  height: 49px;
  top: 0;
  left: 0;
  background-color: #fff1da;
  border-radius: 18px 5px 10px 5px;
  transform: rotate(180deg);
}

.travel-agency .vector-3 {
  position: absolute;
  width: 48px;
  height: 61px;
  top: 6px;
  left: 25px;
}

.travel-agency .text-wrapper-46 {
  position: absolute;
  width: 181px;
  top: 151px;
  left: 24px;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
  color: var(--text-clr);
  font-size: 16px;
  text-align: center;
  letter-spacing: 0;
  line-height: 26px;
}

.travel-agency .text-wrapper-47 {
  position: absolute;
  top: 109px;
  left: 0;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 20px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .category-3 {
  position: absolute;
  width: 201px;
  height: 223px;
  top: 224px;
  left: 905px;
}

.travel-agency .group-7 {
  position: absolute;
  width: 77px;
  height: 73px;
  top: 0;
  left: 98px;
}

.travel-agency .overlap-group-7 {
  position: relative;
  height: 73px;
}

.travel-agency .rectangle-9 {
  position: absolute;
  width: 50px;
  height: 49px;
  top: 24px;
  left: 0;
  background-color: #fff1da;
  border-radius: 18px 5px 10px 5px;
}

.travel-agency .vector-4 {
  position: absolute;
  width: 55px;
  height: 55px;
  top: 0;
  left: 22px;
}

.travel-agency .text-wrapper-48 {
  position: absolute;
  width: 181px;
  top: 145px;
  left: 8px;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
  color: var(--text-clr);
  font-size: 16px;
  text-align: center;
  letter-spacing: 0;
  line-height: 26px;
}

.travel-agency .text-wrapper-49 {
  position: absolute;
  top: 103px;
  left: 0;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 20px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .hero {
  position: absolute;
  width: 1270px;
  height: 4940px;
  top: 0;
  left: 10px;
}

.travel-agency .overlap-11 {
  position: relative;
  height: 4940px;
}

.travel-agency .top-nav {
  position: absolute;
  width: 1270px;
  height: 55px;
  top: 0;
  left: 0;
}

.travel-agency .hero-content {
  position: absolute;
  width: 1201px;
  height: 4924px;
  top: 16px;
  left: 7px;
}

.travel-agency .overlap-12 {
  position: relative;
  height: 4924px;
}

.travel-agency .desc {
  position: absolute;
  width: 696px;
  height: 4770px;
  top: 154px;
  left: 0;
}

.travel-agency .CTA {
  position: absolute;
  width: 649px;
  height: 4295px;
  top: 475px;
  left: 0;
}

.travel-agency .CTA-2 {
  top: -15px;
  left: -35px;
  position: absolute;
  width: 240px;
  height: 130px;
}

.travel-agency .CTA-3 {
  top: 4220px;
  left: 444px;
  position: absolute;
  width: 240px;
  height: 130px;
}

.travel-agency .tagline {
  position: absolute;
  top: 0;
  left: 2px;
  font-family: "Poppins", Helvetica;
  font-weight: 700;
  color: #df6951;
  font-size: 20px;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .desc-2 {
  position: absolute;
  width: 477px;
  top: 351px;
  left: 0;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
  color: #5e6282;
  font-size: 16px;
  letter-spacing: 0;
  line-height: 30px;
}

.travel-agency .overlap-group-8 {
  position: absolute;
  width: 688px;
  height: 178px;
  top: 54px;
  left: 2px;
}

.travel-agency .decore-2 {
  position: absolute;
  width: 385px;
  height: 12px;
  top: 69px;
  left: 303px;
}

.travel-agency .heading-7 {
  top: 0;
  left: 0;
  font-family: "Volkhov", Helvetica;
  color: var(--x-1st);
  font-size: 84px;
  letter-spacing: -3.36px;
  line-height: 89px;
  position: absolute;
  font-weight: 700;
}

.travel-agency .image-2 {
  position: absolute;
  width: 692px;
  height: 785px;
  top: 0;
  left: 509px;
}

.travel-agency .overlap-13 {
  position: relative;
  height: 785px;
}

.travel-agency .plane {
  position: absolute;
  width: 167px;
  height: 153px;
  top: 145px;
  left: 525px;
}

.travel-agency .family-image {
  position: absolute;
  width: 595px;
  height: 785px;
  top: 0;
  left: 0;
  background-image: url(./img/family-holiday-1-1.png);
  background-size: 100% 100%;
}

.travel-agency .plane-2 {
  position: absolute;
  width: 167px;
  height: 153px;
  top: 98px;
  left: 41px;
}

.travel-agency .GOLD-CARD {
  position: absolute;
  width: 351px;
  height: 743px;
  top: 3040px;
  left: 402px;
}

.travel-agency .SILVER-CARD {
  position: absolute;
  width: 351px;
  height: 743px;
  top: 3033px;
  left: 22px;
}

.travel-agency .PLATINUM {
  position: absolute;
  width: 351px;
  height: 743px;
  top: 3046px;
  left: 782px;
}

.travel-agency .text-wrapper-50 {
  position: absolute;
  top: 4010px;
  left: 77px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 26px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .text-wrapper-51 {
  position: absolute;
  top: 4008px;
  left: 474px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 26px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .text-wrapper-52 {
  position: absolute;
  top: 4006px;
  left: 871px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 26px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .text-wrapper-53 {
  top: 4110px;
  left: 77px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 26px;
  text-align: center;
  line-height: normal;
  position: absolute;
  letter-spacing: 0;
}

.travel-agency .text-wrapper-54 {
  top: 4108px;
  left: 474px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 26px;
  text-align: center;
  line-height: normal;
  position: absolute;
  letter-spacing: 0;
}

.travel-agency .text-wrapper-55 {
  top: 4108px;
  left: 871px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 26px;
  text-align: center;
  line-height: normal;
  position: absolute;
  letter-spacing: 0;
}

.travel-agency .text-wrapper-56 {
  top: 4233px;
  left: 77px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 26px;
  text-align: center;
  line-height: normal;
  position: absolute;
  letter-spacing: 0;
}

.travel-agency .text-wrapper-57 {
  position: absolute;
  top: 4231px;
  left: 474px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 26px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .text-wrapper-58 {
  top: 4231px;
  left: 871px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 26px;
  text-align: center;
  line-height: normal;
  position: absolute;
  letter-spacing: 0;
}

.travel-agency .text-wrapper-59 {
  position: absolute;
  top: 4356px;
  left: 81px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 26px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .text-wrapper-60 {
  position: absolute;
  top: 4354px;
  left: 474px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 26px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .text-wrapper-61 {
  position: absolute;
  top: 4354px;
  left: 875px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 26px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .text-wrapper-62 {
  position: absolute;
  top: 4479px;
  left: 81px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 26px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .to-view-all-of-our {
  position: absolute;
  top: 4814px;
  left: 414px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 26px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .text-wrapper-63 {
  position: absolute;
  top: 4479px;
  left: 478px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 26px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .text-wrapper-64 {
  position: absolute;
  top: 4477px;
  left: 875px;
  font-family: "Open Sans", Helvetica;
  font-weight: 600;
  color: var(--text-heading-color);
  font-size: 26px;
  text-align: center;
  letter-spacing: 0;
  line-height: normal;
}

.travel-agency .element-adults-infant {
  top: 4055px;
  left: 79px;
  color: #3dbb74;
  font-size: 16px;
  text-align: center;
  letter-spacing: 0;
  line-height: 12.8px;
  white-space: nowrap;
  position: absolute;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
}

.travel-agency .element-adults-infant-2 {
  top: 4053px;
  left: 476px;
  color: #3dbb74;
  font-size: 16px;
  text-align: center;
  letter-spacing: 0;
  line-height: 12.8px;
  white-space: nowrap;
  position: absolute;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
}

.travel-agency .flexcontainer {
  width: 267px;
  top: 4053px;
  left: 876px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 6px;
  position: absolute;
  height: 51px;
}

.travel-agency .text {
  position: relative;
  align-self: stretch;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
  color: #3dbb74;
  font-size: 16px;
  letter-spacing: 0;
  line-height: 12.8px;
}

.travel-agency .text-wrapper-65 {
  font-family: "Poppins", Helvetica;
  font-weight: 500;
  color: #3dbb74;
  font-size: 16px;
  letter-spacing: 0;
  line-height: 12.8px;
}

.travel-agency .flexcontainer-2 {
  width: 267px;
  top: 4153px;
  left: 876px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 6px;
  position: absolute;
  height: 51px;
}

.travel-agency .flexcontainer-3 {
  width: 297px;
  top: 4276px;
  left: 876px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 6px;
  position: absolute;
  height: 51px;
}

.travel-agency .flexcontainer-4 {
  width: 282px;
  top: 4399px;
  left: 875px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 6px;
  position: absolute;
  height: 51px;
}

.travel-agency .flexcontainer-5 {
  width: 254px;
  top: 4528px;
  left: 876px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 6px;
  position: absolute;
  height: 51px;
}

.travel-agency .element-adults-child-2 {
  top: 4155px;
  left: 81px;
  color: #3dbb74;
  font-size: 16px;
  text-align: center;
  letter-spacing: 0;
  line-height: 12.8px;
  white-space: nowrap;
  position: absolute;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
}

.travel-agency .element-adults-children-2 {
  top: 4153px;
  left: 476px;
  color: #3dbb74;
  font-size: 16px;
  text-align: center;
  letter-spacing: 0;
  line-height: 12.8px;
  white-space: nowrap;
  position: absolute;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
}

.travel-agency .element-adults-children-3 {
  top: 4278px;
  left: 79px;
  color: #3dbb74;
  font-size: 16px;
  text-align: center;
  letter-spacing: 0;
  line-height: 12.8px;
  white-space: nowrap;
  position: absolute;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
}

.travel-agency .element-adults-children-4 {
  top: 4276px;
  left: 478px;
  color: #3dbb74;
  font-size: 16px;
  text-align: center;
  letter-spacing: 0;
  line-height: 12.8px;
  white-space: nowrap;
  position: absolute;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
}

.travel-agency .element-adults-children-5 {
  top: 4401px;
  left: 81px;
  color: #3dbb74;
  font-size: 16px;
  text-align: center;
  letter-spacing: 0;
  line-height: 12.8px;
  white-space: nowrap;
  position: absolute;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
}

.travel-agency .element-adult-children {
  top: 4399px;
  left: 476px;
  color: #3dbb74;
  font-size: 16px;
  text-align: center;
  letter-spacing: 0;
  line-height: 12.8px;
  white-space: nowrap;
  position: absolute;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
}

.travel-agency .element-adult-child {
  top: 4524px;
  left: 81px;
  color: #3dbb74;
  font-size: 16px;
  text-align: center;
  letter-spacing: 0;
  line-height: 12.8px;
  position: absolute;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
}

.travel-agency .element-adults-teenager {
  top: 4521px;
  left: 476px;
  color: #3dbb74;
  font-size: 16px;
  text-align: center;
  letter-spacing: 0;
  line-height: 12.8px;
  white-space: nowrap;
  position: absolute;
  font-family: "Poppins", Helvetica;
  font-weight: 500;
}

.travel-agency .TRIPMILESTONE-2 {
  position: absolute;
  top: 0;
  left: 0;
  font-family: "Poppins", Helvetica;
  font-weight: 800;
  color: transparent;
  font-size: 26px;
  letter-spacing: 1.56px;
  line-height: normal;
}

.travel-agency .rectangle-10 {
  position: absolute;
  width: 166px;
  height: 51px;
  top: 4589px;
  left: 517px;
  border-radius: 26px;
  border: 3px solid;
  border-color: #3dbb74;
}
