/**
 * Direct Database Service for Family EMI
 * Connects directly to Supabase databases for live data
 */

class DatabaseService {
  constructor() {
    // Initialize Supabase clients
    this.initializeClients();
    this.sessionId = this.generateSessionId();
    
    console.log('🗄️ Database Service initialized');
  }
  
  initializeClients() {
    // Check if CONFIG is available
    if (typeof CONFIG === 'undefined') {
      console.error('❌ CONFIG not loaded. Please include config.js before databaseService.js');
      return;
    }

    // Validate configuration
    if (CONFIG.CRM_ANON_KEY === 'YOUR_CRM_DATABASE_ANON_KEY_HERE' ||
        CONFIG.QUOTE_ANON_KEY === 'YOUR_QUOTE_DATABASE_ANON_KEY_HERE') {
      console.warn('⚠️ Please update the database keys in js/config.js');
    }

    // CRM Database (Family Types)
    this.crmDB = supabase.createClient(
      CONFIG.CRM_DB_URL,
      CONFIG.CRM_ANON_KEY
    );

    // Quote Database (Packages & EMI)
    this.quoteDB = supabase.createClient(
      CONFIG.QUOTE_DB_URL,
      CONFIG.QUOTE_ANON_KEY
    );

    console.log('🔗 Database clients initialized');
  }
  
  generateSessionId() {
    return `sess_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  
  // Get all family types from CRM database
  async getFamilyTypes() {
    try {
      console.log('📊 Fetching family types from CRM database...');
      
      const { data, error } = await this.crmDB
        .from('family_type')
        .select('*')
        .order('family_type');
      
      if (error) {
        console.error('❌ Error fetching family types:', error);
        throw error;
      }
      
      console.log('✅ Loaded family types:', data.length);
      
      // Format data for frontend
      const formattedData = data.map(ft => ({
        ...ft,
        composition: this.formatFamilyComposition(ft)
      }));
      
      return { success: true, data: formattedData };
    } catch (error) {
      console.error('Database error in getFamilyTypes:', error);
      return { success: false, error: error.message };
    }
  }
  
  // Get destinations from quote database
  async getDestinations() {
    try {
      console.log('🗺️ Fetching destinations from Quote database...');

      // First, let's check if the table exists and what data it contains
      const { data, error } = await this.quoteDB
        .from('family_type_prices')
        .select('*')
        .limit(10);

      if (error) {
        console.error('❌ Error fetching from family_type_prices:', error);
        console.log('🔍 Trying alternative approach - checking quotes table...');

        // Fallback: Try to get destinations from quotes table
        const { data: quotesData, error: quotesError } = await this.quoteDB
          .from('quotes')
          .select('destination')
          .not('destination', 'is', null)
          .limit(20);

        if (quotesError) {
          console.error('❌ Error fetching from quotes table:', quotesError);
          // Return some default destinations
          return {
            success: true,
            data: [
              { destination: 'Kashmir', category: 'Hill Station' },
              { destination: 'Goa', category: 'Beach' },
              { destination: 'Manali', category: 'Hill Station' },
              { destination: 'Kerala', category: 'Backwaters' },
              { destination: 'Rajasthan', category: 'Desert' }
            ]
          };
        }

        // Get unique destinations from quotes table
        const destinations = [...new Set(quotesData.map(item => item.destination))]
          .filter(dest => dest && dest.trim() !== '')
          .map(dest => ({
            destination: dest,
            category: 'General'
          }))
          .sort((a, b) => a.destination.localeCompare(b.destination));

        console.log('✅ Loaded destinations from quotes table:', destinations.length);
        return { success: true, data: destinations };
      }

      console.log('📊 Sample data from family_type_prices:', data);
      console.log('📊 Table columns:', data.length > 0 ? Object.keys(data[0]) : 'No data');

      if (!data || data.length === 0) {
        console.warn('⚠️ No data found in family_type_prices table, using fallback destinations');
        return {
          success: true,
          data: [
            { destination: 'Kashmir', category: 'Hill Station' },
            { destination: 'Goa', category: 'Beach' },
            { destination: 'Manali', category: 'Hill Station' },
            { destination: 'Kerala', category: 'Backwaters' },
            { destination: 'Rajasthan', category: 'Desert' }
          ]
        };
      }

      // Check if destination column exists
      const firstRow = data[0];
      const hasDestination = firstRow && ('destination' in firstRow || 'destination_category' in firstRow);

      if (!hasDestination) {
        console.warn('⚠️ No destination column found, using fallback destinations');
        return {
          success: true,
          data: [
            { destination: 'Kashmir', category: 'Hill Station' },
            { destination: 'Goa', category: 'Beach' },
            { destination: 'Manali', category: 'Hill Station' }
          ]
        };
      }

      // Get unique destinations from the actual data
      const destinations = [...new Set(data.map(item => item.destination || item.destination_category))]
        .filter(dest => dest && dest.trim() !== '')
        .map(dest => ({
          destination: dest,
          category: 'General'
        }))
        .sort((a, b) => a.destination.localeCompare(b.destination));

      console.log('✅ Loaded destinations:', destinations.length);

      return { success: true, data: destinations };
    } catch (error) {
      console.error('Database error in getDestinations:', error);
      return { success: false, error: error.message };
    }
  }
  
  // Search packages based on criteria
  async searchPackages(searchParams) {
    try {
      console.log('🔍 Searching packages with params:', searchParams);
      
      const { destination, adults, children, infants } = searchParams;
      
      // Step 1: Detect family type
      const familyType = await this.detectFamilyType(adults, children || 0, infants || 0);
      console.log('🎯 Detected family type:', familyType);

      // Step 2: Check if family_type_prices table has data
      console.log('🔍 Checking family_type_prices table...');
      const { data: sampleData, error: sampleError } = await this.quoteDB
        .from('family_type_prices')
        .select('*')
        .limit(5);

      if (sampleError) {
        console.error('❌ Error accessing family_type_prices table:', sampleError);
        console.log('🔄 Trying alternative approach with quotes table...');

        // Fallback: Create sample packages from quotes table
        const { data: quotesData, error: quotesError } = await this.quoteDB
          .from('quotes')
          .select('*')
          .ilike('destination', `%${destination}%`)
          .limit(10);

        if (quotesError) {
          console.error('❌ Error accessing quotes table:', quotesError);
          // Return sample packages
          return this.createSamplePackages(destination, familyType);
        }

        // Convert quotes to packages format
        const packages = quotesData.map(quote => this.convertQuoteToPackage(quote, familyType));
        console.log('✅ Created packages from quotes:', packages.length);

        return {
          success: true,
          matched_family_type: familyType,
          packages: packages,
          search_params: searchParams
        };
      }

      console.log('📊 Sample data from family_type_prices:', sampleData);
      console.log('📊 Available columns:', sampleData.length > 0 ? Object.keys(sampleData[0]) : 'No data');

      if (!sampleData || sampleData.length === 0) {
        console.warn('⚠️ No data in family_type_prices table, creating sample packages');
        return this.createSamplePackages(destination, familyType, searchParams);
      }

      // Step 3: Search packages in family_type_prices
      let query = this.quoteDB
        .from('family_type_prices')
        .select('*');

      // Check if destination column exists
      const hasDestination = sampleData[0] && 'destination' in sampleData[0];

      if (hasDestination && destination) {
        query = query.ilike('destination', `%${destination}%`);
      }

      // Limit results
      query = query.limit(20);

      console.log('🔍 Executing query for destination:', destination);
      const { data: packages, error } = await query;

      if (error) {
        console.error('❌ Error searching packages:', error);
        throw error;
      }

      // Step 3: Get EMI plans separately if packages found
      let packagesWithEMI = packages;
      if (packages && packages.length > 0) {
        try {
          // Try to get EMI plans separately
          const { data: emiPlans, error: emiError } = await this.quoteDB
            .from('family_type_emi_plans')
            .select('*');

          if (!emiError && emiPlans) {
            // Attach EMI plans to packages (you can customize this logic)
            packagesWithEMI = packages.map(pkg => ({
              ...pkg,
              family_type_emi_plans: emiPlans.slice(0, 3) // Take first 3 EMI plans as sample
            }));
          }
        } catch (emiError) {
          console.warn('⚠️ Could not load EMI plans, using packages without EMI:', emiError);
        }
      }
      
      console.log('✅ Found packages:', packagesWithEMI.length);

      // Format packages for frontend
      const formattedPackages = packagesWithEMI.map(pkg => this.formatPackageForFrontend(pkg));
      
      return {
        success: true,
        matched_family_type: familyType,
        packages: formattedPackages,
        search_params: searchParams
      };
    } catch (error) {
      console.error('Database error in searchPackages:', error);
      return { success: false, error: error.message };
    }
  }
  
  // Get package details
  async getPackageDetails(packageId) {
    try {
      console.log('📦 Fetching package details for ID:', packageId);
      
      const { data: packageData, error } = await this.quoteDB
        .from('family_type_prices')
        .select(`
          *,
          family_type_emi_plans(*)
        `)
        .eq('id', packageId)
        .single();
      
      if (error) {
        console.error('❌ Error fetching package details:', error);
        throw error;
      }
      
      if (!packageData) {
        throw new Error('Package not found');
      }
      
      console.log('✅ Loaded package details:', packageData.destination);
      
      const formattedPackage = this.formatPackageDetailsForFrontend(packageData);
      
      return { success: true, package: formattedPackage };
    } catch (error) {
      console.error('Database error in getPackageDetails:', error);
      return { success: false, error: error.message };
    }
  }
  
  // Submit quote request
  async submitQuoteRequest(quoteData) {
    try {
      console.log('📝 Submitting quote request:', quoteData);
      
      // Insert into public_family_quotes table
      const { data, error } = await this.quoteDB
        .from('public_family_quotes')
        .insert({
          customer_email: quoteData.customer_email,
          customer_phone: quoteData.customer_phone,
          customer_name: quoteData.customer_name,
          destination: quoteData.destination,
          travel_date: quoteData.travel_date,
          no_of_adults: quoteData.adults,
          no_of_children: quoteData.children || 0,
          no_of_infants: quoteData.infants || 0,
          matched_price_id: quoteData.selected_package_id,
          selected_emi_plan_id: quoteData.selected_emi_plan_id,
          utm_source: this.getUtmSource(),
          session_id: this.sessionId,
          lead_source: 'family_website',
          created_at: new Date().toISOString()
        })
        .select()
        .single();
      
      if (error) {
        console.error('❌ Error submitting quote request:', error);
        throw error;
      }
      
      console.log('✅ Quote request submitted successfully:', data.id);
      
      return {
        success: true,
        quote_id: data.id,
        message: 'Quote request submitted successfully! Our team will contact you soon.'
      };
    } catch (error) {
      console.error('Database error in submitQuoteRequest:', error);
      return { success: false, error: error.message };
    }
  }
  
  // Helper: Detect family type based on traveler counts
  async detectFamilyType(adults, children, infants) {
    try {
      const familyTypesResponse = await this.getFamilyTypes();
      
      if (!familyTypesResponse.success) {
        return this.getDefaultFamilyType(adults, children, infants);
      }
      
      const familyTypes = familyTypesResponse.data;
      
      // Find exact match first
      let match = familyTypes.find(ft => 
        ft.no_of_adults === adults && 
        ft.no_of_children === children && 
        ft.no_of_infants === infants
      );
      
      // If no exact match, find closest match
      if (!match) {
        match = familyTypes.find(ft => 
          ft.no_of_adults === adults && 
          ft.no_of_children >= children && 
          ft.no_of_infants >= infants
        );
      }
      
      // Default to first family type if no match
      if (!match) {
        match = familyTypes[0] || this.getDefaultFamilyType(adults, children, infants);
      }
      
      return match;
    } catch (error) {
      console.error('Error detecting family type:', error);
      return this.getDefaultFamilyType(adults, children, infants);
    }
  }
  
  // Helper: Get default family type
  getDefaultFamilyType(adults, children, infants) {
    return {
      family_id: 'CUSTOM',
      family_type: 'Custom Family',
      no_of_adults: adults,
      no_of_children: children,
      no_of_infants: infants,
      composition: this.formatFamilyComposition({ no_of_adults: adults, no_of_children: children, no_of_infants: infants })
    };
  }
  
  // Helper: Format family composition
  formatFamilyComposition(familyType) {
    let composition = `${familyType.no_of_adults} Adult${familyType.no_of_adults > 1 ? 's' : ''}`;
    
    if (familyType.no_of_children > 0) {
      composition += ` + ${familyType.no_of_children} Child${familyType.no_of_children > 1 ? 'ren' : ''}`;
    }
    
    if (familyType.no_of_infants > 0) {
      composition += ` + ${familyType.no_of_infants} Infant${familyType.no_of_infants > 1 ? 's' : ''}`;
    }
    
    return composition;
  }
  
  // Helper: Format package for frontend
  formatPackageForFrontend(packageData) {
    return {
      id: packageData.id,
      title: packageData.package_title || `${packageData.destination} Package`,
      destination: packageData.destination,
      duration_days: packageData.package_duration_days || 5,
      total_price: packageData.total_price,
      family_type: packageData.family_type_name,
      emi_options: (packageData.family_type_emi_plans || []).map(emi => ({
        id: emi.id,
        months: emi.emi_months,
        monthly_amount: emi.monthly_amount,
        total_amount: emi.total_amount,
        processing_fee: emi.processing_fee || 0,
        label: emi.marketing_label || `${emi.emi_months} Months`,
        is_featured: emi.is_featured || false
      })),
      inclusions: ['Flights', 'Hotels', 'Meals', 'Sightseeing'],
      images: ['/img/rectangle-14.png'], // Default image
      offer_badge: packageData.total_price > 40000 ? '15% OFF' : 'Best Value'
    };
  }
  
  // Helper: Format package details for frontend
  formatPackageDetailsForFrontend(packageData) {
    const basePackage = this.formatPackageForFrontend(packageData);
    
    return {
      ...basePackage,
      description: `Experience the beauty of ${packageData.destination} with our carefully crafted family package.`,
      itinerary: [
        {
          day: 1,
          title: `Arrival in ${packageData.destination}`,
          description: 'Airport pickup, hotel check-in, welcome dinner'
        },
        {
          day: 2,
          title: 'Sightseeing Tour',
          description: 'Visit famous attractions and local markets'
        },
        {
          day: 3,
          title: 'Adventure Activities',
          description: 'Enjoy adventure sports and outdoor activities'
        }
      ],
      inclusions: [
        'Round-trip flights',
        '4-star hotel accommodation',
        'Daily breakfast and dinner',
        'All sightseeing and transfers',
        'Professional guide'
      ],
      exclusions: [
        'Personal expenses',
        'Travel insurance',
        'Lunch (except on specified days)',
        'Tips and gratuities'
      ]
    };
  }
  
  // Helper: Get UTM source
  getUtmSource() {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('utm_source') || 'direct';
  }

  // Helper: Create sample packages when database is empty
  createSamplePackages(destination, familyType, searchParams) {
    console.log('🎨 Creating sample packages for:', destination);

    const samplePackages = [
      {
        id: 'sample-1',
        title: `${destination} Family Package`,
        destination: destination,
        duration_days: 5,
        total_price: 45000,
        family_type: familyType.family_type,
        emi_options: [
          {
            id: 'emi-1',
            months: 3,
            monthly_amount: 15000,
            total_amount: 45000,
            processing_fee: 1000,
            label: 'Quick Pay',
            is_featured: false
          },
          {
            id: 'emi-2',
            months: 6,
            monthly_amount: 7500,
            total_amount: 45000,
            processing_fee: 1500,
            label: 'Best Value',
            is_featured: true
          },
          {
            id: 'emi-3',
            months: 12,
            monthly_amount: 3750,
            total_amount: 45000,
            processing_fee: 2000,
            label: 'Low Monthly',
            is_featured: false
          }
        ],
        inclusions: ['Flights', 'Hotels', 'Meals', 'Sightseeing'],
        images: ['/img/rectangle-14.png'],
        offer_badge: '15% OFF'
      },
      {
        id: 'sample-2',
        title: `${destination} Adventure Package`,
        destination: destination,
        duration_days: 7,
        total_price: 65000,
        family_type: familyType.family_type,
        emi_options: [
          {
            id: 'emi-4',
            months: 6,
            monthly_amount: 10833,
            total_amount: 65000,
            processing_fee: 2000,
            label: 'Popular',
            is_featured: true
          }
        ],
        inclusions: ['Flights', 'Hotels', 'Meals', 'Adventure Activities'],
        images: ['/img/rectangle-14-2.png'],
        offer_badge: 'Best Value'
      }
    ];

    return {
      success: true,
      matched_family_type: familyType,
      packages: samplePackages,
      search_params: searchParams
    };
  }

  // Helper: Convert quote to package format
  convertQuoteToPackage(quote, familyType) {
    return {
      id: quote.id,
      title: `${quote.destination} Package`,
      destination: quote.destination,
      duration_days: quote.duration_days || 5,
      total_price: quote.total_cost || 45000,
      family_type: familyType.family_type,
      emi_options: [
        {
          id: `emi-${quote.id}`,
          months: 6,
          monthly_amount: Math.round((quote.total_cost || 45000) / 6),
          total_amount: quote.total_cost || 45000,
          processing_fee: Math.round((quote.total_cost || 45000) * 0.02),
          label: 'Standard',
          is_featured: true
        }
      ],
      inclusions: ['Flights', 'Hotels', 'Meals', 'Sightseeing'],
      images: ['/img/rectangle-14.png'],
      offer_badge: 'Available'
    };
  }
}

// Create global database service instance
const databaseService = new DatabaseService();

// Export for use in other scripts
window.databaseService = databaseService;

console.log('🚀 Database Service loaded successfully');
