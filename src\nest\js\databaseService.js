/**
 * Direct Database Service for Family EMI
 * Connects directly to Supabase databases for live data
 */

class DatabaseService {
  constructor() {
    // Initialize Supabase clients
    this.initializeClients();
    this.sessionId = this.generateSessionId();
    
    console.log('🗄️ Database Service initialized');
  }
  
  initializeClients() {
    // Check if CONFIG is available
    if (typeof CONFIG === 'undefined') {
      console.error('❌ CONFIG not loaded. Please include config.js before databaseService.js');
      return;
    }

    // Validate configuration
    if (CONFIG.CRM_ANON_KEY === 'YOUR_CRM_DATABASE_ANON_KEY_HERE' ||
        CONFIG.QUOTE_ANON_KEY === 'YOUR_QUOTE_DATABASE_ANON_KEY_HERE') {
      console.warn('⚠️ Please update the database keys in js/config.js');
    }

    // CRM Database (Family Types)
    this.crmDB = supabase.createClient(
      CONFIG.CRM_DB_URL,
      CONFIG.CRM_ANON_KEY
    );

    // Quote Database (Packages & EMI)
    this.quoteDB = supabase.createClient(
      CONFIG.QUOTE_DB_URL,
      CONFIG.QUOTE_ANON_KEY
    );

    console.log('🔗 Database clients initialized');
  }
  
  generateSessionId() {
    return `sess_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  
  // Get all family types from CRM database
  async getFamilyTypes() {
    try {
      console.log('📊 Fetching family types from CRM database...');
      
      const { data, error } = await this.crmDB
        .from('family_type')
        .select('*')
        .order('family_type');
      
      if (error) {
        console.error('❌ Error fetching family types:', error);
        throw error;
      }
      
      console.log('✅ Loaded family types:', data.length);
      
      // Format data for frontend
      const formattedData = data.map(ft => ({
        ...ft,
        composition: this.formatFamilyComposition(ft)
      }));
      
      return { success: true, data: formattedData };
    } catch (error) {
      console.error('Database error in getFamilyTypes:', error);
      return { success: false, error: error.message };
    }
  }
  
  // Get destinations from quote database
  async getDestinations() {
    try {
      console.log('🗺️ Fetching destinations from Quote database...');
      
      const { data, error } = await this.quoteDB
        .from('family_type_prices')
        .select('destination, destination_category')
        .not('destination', 'is', null);
      
      if (error) {
        console.error('❌ Error fetching destinations:', error);
        throw error;
      }
      
      // Get unique destinations
      const uniqueDestinations = [...new Set(data.map(item => item.destination))]
        .filter(dest => dest && dest.trim() !== '')
        .map(dest => ({
          destination: dest,
          category: data.find(item => item.destination === dest)?.destination_category || 'General'
        }))
        .sort((a, b) => a.destination.localeCompare(b.destination));
      
      console.log('✅ Loaded destinations:', uniqueDestinations.length);
      
      return { success: true, data: uniqueDestinations };
    } catch (error) {
      console.error('Database error in getDestinations:', error);
      return { success: false, error: error.message };
    }
  }
  
  // Search packages based on criteria
  async searchPackages(searchParams) {
    try {
      console.log('🔍 Searching packages with params:', searchParams);
      
      const { destination, adults, children, infants } = searchParams;
      
      // Step 1: Detect family type
      const familyType = await this.detectFamilyType(adults, children || 0, infants || 0);
      
      // Step 2: Search packages in family_type_prices
      let query = this.quoteDB
        .from('family_type_prices')
        .select(`
          *,
          family_type_emi_plans(*)
        `);
      
      // Filter by destination (case-insensitive partial match)
      if (destination) {
        query = query.ilike('destination', `%${destination}%`);
      }
      
      // Limit results
      query = query.limit(20);
      
      const { data: packages, error } = await query;
      
      if (error) {
        console.error('❌ Error searching packages:', error);
        throw error;
      }
      
      console.log('✅ Found packages:', packages.length);
      
      // Format packages for frontend
      const formattedPackages = packages.map(pkg => this.formatPackageForFrontend(pkg));
      
      return {
        success: true,
        matched_family_type: familyType,
        packages: formattedPackages,
        search_params: searchParams
      };
    } catch (error) {
      console.error('Database error in searchPackages:', error);
      return { success: false, error: error.message };
    }
  }
  
  // Get package details
  async getPackageDetails(packageId) {
    try {
      console.log('📦 Fetching package details for ID:', packageId);
      
      const { data: packageData, error } = await this.quoteDB
        .from('family_type_prices')
        .select(`
          *,
          family_type_emi_plans(*)
        `)
        .eq('id', packageId)
        .single();
      
      if (error) {
        console.error('❌ Error fetching package details:', error);
        throw error;
      }
      
      if (!packageData) {
        throw new Error('Package not found');
      }
      
      console.log('✅ Loaded package details:', packageData.destination);
      
      const formattedPackage = this.formatPackageDetailsForFrontend(packageData);
      
      return { success: true, package: formattedPackage };
    } catch (error) {
      console.error('Database error in getPackageDetails:', error);
      return { success: false, error: error.message };
    }
  }
  
  // Submit quote request
  async submitQuoteRequest(quoteData) {
    try {
      console.log('📝 Submitting quote request:', quoteData);
      
      // Insert into public_family_quotes table
      const { data, error } = await this.quoteDB
        .from('public_family_quotes')
        .insert({
          customer_email: quoteData.customer_email,
          customer_phone: quoteData.customer_phone,
          customer_name: quoteData.customer_name,
          destination: quoteData.destination,
          travel_date: quoteData.travel_date,
          no_of_adults: quoteData.adults,
          no_of_children: quoteData.children || 0,
          no_of_infants: quoteData.infants || 0,
          matched_price_id: quoteData.selected_package_id,
          selected_emi_plan_id: quoteData.selected_emi_plan_id,
          utm_source: this.getUtmSource(),
          session_id: this.sessionId,
          lead_source: 'family_website',
          created_at: new Date().toISOString()
        })
        .select()
        .single();
      
      if (error) {
        console.error('❌ Error submitting quote request:', error);
        throw error;
      }
      
      console.log('✅ Quote request submitted successfully:', data.id);
      
      return {
        success: true,
        quote_id: data.id,
        message: 'Quote request submitted successfully! Our team will contact you soon.'
      };
    } catch (error) {
      console.error('Database error in submitQuoteRequest:', error);
      return { success: false, error: error.message };
    }
  }
  
  // Helper: Detect family type based on traveler counts
  async detectFamilyType(adults, children, infants) {
    try {
      const familyTypesResponse = await this.getFamilyTypes();
      
      if (!familyTypesResponse.success) {
        return this.getDefaultFamilyType(adults, children, infants);
      }
      
      const familyTypes = familyTypesResponse.data;
      
      // Find exact match first
      let match = familyTypes.find(ft => 
        ft.no_of_adults === adults && 
        ft.no_of_children === children && 
        ft.no_of_infants === infants
      );
      
      // If no exact match, find closest match
      if (!match) {
        match = familyTypes.find(ft => 
          ft.no_of_adults === adults && 
          ft.no_of_children >= children && 
          ft.no_of_infants >= infants
        );
      }
      
      // Default to first family type if no match
      if (!match) {
        match = familyTypes[0] || this.getDefaultFamilyType(adults, children, infants);
      }
      
      return match;
    } catch (error) {
      console.error('Error detecting family type:', error);
      return this.getDefaultFamilyType(adults, children, infants);
    }
  }
  
  // Helper: Get default family type
  getDefaultFamilyType(adults, children, infants) {
    return {
      family_id: 'CUSTOM',
      family_type: 'Custom Family',
      no_of_adults: adults,
      no_of_children: children,
      no_of_infants: infants,
      composition: this.formatFamilyComposition({ no_of_adults: adults, no_of_children: children, no_of_infants: infants })
    };
  }
  
  // Helper: Format family composition
  formatFamilyComposition(familyType) {
    let composition = `${familyType.no_of_adults} Adult${familyType.no_of_adults > 1 ? 's' : ''}`;
    
    if (familyType.no_of_children > 0) {
      composition += ` + ${familyType.no_of_children} Child${familyType.no_of_children > 1 ? 'ren' : ''}`;
    }
    
    if (familyType.no_of_infants > 0) {
      composition += ` + ${familyType.no_of_infants} Infant${familyType.no_of_infants > 1 ? 's' : ''}`;
    }
    
    return composition;
  }
  
  // Helper: Format package for frontend
  formatPackageForFrontend(packageData) {
    return {
      id: packageData.id,
      title: packageData.package_title || `${packageData.destination} Package`,
      destination: packageData.destination,
      duration_days: packageData.package_duration_days || 5,
      total_price: packageData.total_price,
      family_type: packageData.family_type_name,
      emi_options: (packageData.family_type_emi_plans || []).map(emi => ({
        id: emi.id,
        months: emi.emi_months,
        monthly_amount: emi.monthly_amount,
        total_amount: emi.total_amount,
        processing_fee: emi.processing_fee || 0,
        label: emi.marketing_label || `${emi.emi_months} Months`,
        is_featured: emi.is_featured || false
      })),
      inclusions: ['Flights', 'Hotels', 'Meals', 'Sightseeing'],
      images: ['/img/rectangle-14.png'], // Default image
      offer_badge: packageData.total_price > 40000 ? '15% OFF' : 'Best Value'
    };
  }
  
  // Helper: Format package details for frontend
  formatPackageDetailsForFrontend(packageData) {
    const basePackage = this.formatPackageForFrontend(packageData);
    
    return {
      ...basePackage,
      description: `Experience the beauty of ${packageData.destination} with our carefully crafted family package.`,
      itinerary: [
        {
          day: 1,
          title: `Arrival in ${packageData.destination}`,
          description: 'Airport pickup, hotel check-in, welcome dinner'
        },
        {
          day: 2,
          title: 'Sightseeing Tour',
          description: 'Visit famous attractions and local markets'
        },
        {
          day: 3,
          title: 'Adventure Activities',
          description: 'Enjoy adventure sports and outdoor activities'
        }
      ],
      inclusions: [
        'Round-trip flights',
        '4-star hotel accommodation',
        'Daily breakfast and dinner',
        'All sightseeing and transfers',
        'Professional guide'
      ],
      exclusions: [
        'Personal expenses',
        'Travel insurance',
        'Lunch (except on specified days)',
        'Tips and gratuities'
      ]
    };
  }
  
  // Helper: Get UTM source
  getUtmSource() {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('utm_source') || 'direct';
  }
}

// Create global database service instance
const databaseService = new DatabaseService();

// Export for use in other scripts
window.databaseService = databaseService;

console.log('🚀 Database Service loaded successfully');
