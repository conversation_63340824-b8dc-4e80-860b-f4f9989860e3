// Database Table Checker Script
// This script checks what tables exist in both CRM and Quote databases

import { createClient } from '@supabase/supabase-js';

// CRM Database Configuration
const CRM_URL = 'https://tlfwcnikdlwoliqzavxj.supabase.co';
const CRM_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRsZndjbmlrZGx3b2xpcXphdnhqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQ4NTcwMjgsImV4cCI6MjA2MDQzMzAyOH0.fCaJNbHL6VwKxTbt3vYl2F5O2gRoMFuUO1bhqEtSWpI';

// Quote Database Configuration  
const QUOTE_URL = 'https://lkqbrlrmrsnbtkoryazq.supabase.co';
const QUOTE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxrcWJybHJtcnNuYnRrb3J5YXpxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU0MDA2ODYsImV4cCI6MjA2MDk3NjY4Nn0.0E4Z87L9j32k3jKa15n4LpmFsVx8YCJuwovi-mSw4SE';

// Create clients
const crmClient = createClient(CRM_URL, CRM_KEY);
const quoteClient = createClient(QUOTE_URL, QUOTE_KEY);

async function checkTables() {
  console.log('🔍 CHECKING DATABASE TABLES...\n');

  // Check CRM Database Tables
  console.log('📊 CRM DATABASE TABLES:');
  console.log('URL:', CRM_URL);
  try {
    const { data: crmTables, error: crmError } = await crmClient
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .order('table_name');

    if (crmError) {
      console.error('❌ CRM Error:', crmError.message);
    } else {
      console.log('✅ CRM Tables found:', crmTables?.length || 0);
      crmTables?.forEach(table => console.log(`  - ${table.table_name}`));
    }
  } catch (error) {
    console.error('❌ CRM Exception:', error.message);
  }

  console.log('\n📊 QUOTE DATABASE TABLES:');
  console.log('URL:', QUOTE_URL);
  try {
    const { data: quoteTables, error: quoteError } = await quoteClient
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .order('table_name');

    if (quoteError) {
      console.error('❌ Quote Error:', quoteError.message);
    } else {
      console.log('✅ Quote Tables found:', quoteTables?.length || 0);
      quoteTables?.forEach(table => console.log(`  - ${table.table_name}`));
    }
  } catch (error) {
    console.error('❌ Quote Exception:', error.message);
  }

  // Check specific tables we need
  console.log('\n🎯 CHECKING SPECIFIC TABLES...');
  
  // Check family_type table in CRM
  try {
    const { data: familyTypes, error: ftError } = await crmClient
      .from('family_type')
      .select('family_id, family_type')
      .limit(5);

    if (ftError) {
      console.log('❌ family_type table not found in CRM:', ftError.message);
    } else {
      console.log('✅ family_type table exists in CRM with', familyTypes?.length || 0, 'sample records');
    }
  } catch (error) {
    console.log('❌ family_type table check failed:', error.message);
  }

  // Check family_type_prices table in Quote DB
  try {
    const { data: familyPrices, error: fpError } = await quoteClient
      .from('family_type_prices')
      .select('id, family_type_name')
      .limit(5);

    if (fpError) {
      console.log('❌ family_type_prices table not found in Quote DB:', fpError.message);
    } else {
      console.log('✅ family_type_prices table exists in Quote DB with', familyPrices?.length || 0, 'sample records');
    }
  } catch (error) {
    console.log('❌ family_type_prices table check failed:', error.message);
  }

  // Check quote_mappings table in Quote DB
  try {
    const { data: quoteMappings, error: qmError } = await quoteClient
      .from('quote_mappings')
      .select('id, quote_name')
      .limit(5);

    if (qmError) {
      console.log('❌ quote_mappings table not found in Quote DB:', qmError.message);
    } else {
      console.log('✅ quote_mappings table exists in Quote DB with', quoteMappings?.length || 0, 'sample records');
    }
  } catch (error) {
    console.log('❌ quote_mappings table check failed:', error.message);
  }

  // Check quotes table in Quote DB
  try {
    const { data: quotes, error: qError } = await quoteClient
      .from('quotes')
      .select('id, customer_name')
      .limit(5);

    if (qError) {
      console.log('❌ quotes table not found in Quote DB:', qError.message);
    } else {
      console.log('✅ quotes table exists in Quote DB with', quotes?.length || 0, 'sample records');
    }
  } catch (error) {
    console.log('❌ quotes table check failed:', error.message);
  }

  console.log('\n✅ Database check complete!');
}

// Run the check
checkTables().catch(console.error);
