// SIMPLE Family Type Calculator - Just use database data + Quote Generator logic
import { getQuoteClient, getCrmClient } from '../../lib/supabaseManager';

// Simple interfaces - just what we need
export interface FamilyTypeDB {
  family_id: string;
  family_type: string;
  no_of_adults: number;
  no_of_infants: number;
  no_of_child: number;
  no_of_children: number;
  family_count: number;
  cab_type: string;
  cab_capacity: number;
  rooms_need: number;
}

export interface BaselineQuote {
  id: string;
  customer_name: string;
  destination: string;
  total_cost: number;
  no_of_persons: number;
  extra_adults: number;
  children: number;
  infants: number;
}

export interface FamilyTypeResult {
  familyType: FamilyTypeDB;
  calculatedPrice: number;
  breakdown: {
    hotelCost: number;
    vehicleCost: number;
    otherCosts: number;
    total: number;
  };
}

// Step 1: Get family types from database (simple)
export const getFamilyTypesFromDB = async (): Promise<FamilyTypeDB[]> => {
  try {
    const supabase = getCrmClient();
    if (!supabase) {
      console.error('❌ CRM client not available');
      return [];
    }

    const { data, error } = await supabase
      .from('family_type')
      .select('*')
      .order('family_id');

    if (error) {
      console.error('❌ Error fetching family types:', error);
      return [];
    }

    console.log(`✅ Got ${data?.length || 0} family types from database`);
    return data || [];
  } catch (error) {
    console.error('❌ Exception getting family types:', error);
    return [];
  }
};

// Step 2: Calculate price for one family type (simple Quote Generator logic)
export const calculateFamilyTypePrice = (
  familyType: FamilyTypeDB, 
  baselineQuote: BaselineQuote
): FamilyTypeResult => {
  
  console.log(`\n💰 Calculating price for: ${familyType.family_type}`);
  
  // Use database values directly - no complexity!
  const rooms = familyType.rooms_need;
  const adults = familyType.no_of_adults;
  const children = familyType.no_of_children + familyType.no_of_child;
  const infants = familyType.no_of_infants;
  const familyCount = familyType.family_count;
  const cabType = familyType.cab_type;

  console.log(`👥 Family: ${adults}A + ${children}C + ${infants}I = ${familyCount} people`);
  console.log(`🏨 Rooms: ${rooms} | 🚗 Vehicle: ${cabType}`);

  // Calculate costs using same Quote Generator percentages
  const baselineTotal = baselineQuote.total_cost;
  const baselinePeople = baselineQuote.no_of_persons + baselineQuote.extra_adults + baselineQuote.children + baselineQuote.infants;
  
  // Scale based on people count (simple scaling)
  const peopleRatio = familyCount / Math.max(baselinePeople, 1);
  
  // Hotel cost (40% of total) - scale by rooms
  const baselineRooms = Math.ceil((baselineQuote.no_of_persons + baselineQuote.extra_adults) / 2);
  const roomRatio = rooms / Math.max(baselineRooms, 1);
  const hotelCost = (baselineTotal * 0.4) * roomRatio;
  
  // Vehicle cost (25% of total) - scale by vehicle type
  let vehicleMultiplier = 1.0;
  if (cabType.toLowerCase().includes('innova')) vehicleMultiplier = 1.2;
  else if (cabType.toLowerCase().includes('suv')) vehicleMultiplier = 1.25;
  else if (cabType.toLowerCase().includes('tempo')) vehicleMultiplier = 1.4;
  else if (cabType.toLowerCase().includes('bus')) vehicleMultiplier = 1.6;
  
  const vehicleCost = (baselineTotal * 0.25) * vehicleMultiplier;
  
  // Other costs (35% of total) - scale by people
  const otherCosts = (baselineTotal * 0.35) * peopleRatio;
  
  // Total price
  const totalPrice = hotelCost + vehicleCost + otherCosts;
  
  console.log(`💰 Result: Hotel ₹${Math.round(hotelCost)} + Vehicle ₹${Math.round(vehicleCost)} + Other ₹${Math.round(otherCosts)} = ₹${Math.round(totalPrice)}`);

  return {
    familyType,
    calculatedPrice: Math.round(totalPrice),
    breakdown: {
      hotelCost: Math.round(hotelCost),
      vehicleCost: Math.round(vehicleCost),
      otherCosts: Math.round(otherCosts),
      total: Math.round(totalPrice)
    }
  };
};

// Step 3: Calculate for all family types (simple loop)
export const calculateAllFamilyTypePrices = async (
  baselineQuote: BaselineQuote
): Promise<FamilyTypeResult[]> => {
  
  console.log(`\n🎯 === SIMPLE FAMILY TYPE CALCULATION ===`);
  console.log(`📊 Baseline: ${baselineQuote.customer_name} - ₹${baselineQuote.total_cost}`);

  // Get all family types
  const familyTypes = await getFamilyTypesFromDB();
  if (familyTypes.length === 0) {
    console.error('❌ No family types found');
    return [];
  }

  const results: FamilyTypeResult[] = [];

  // Calculate for each family type
  for (const familyType of familyTypes) {
    try {
      const result = calculateFamilyTypePrice(familyType, baselineQuote);
      results.push(result);
    } catch (error) {
      console.error(`❌ Error calculating ${familyType.family_type}:`, error);
    }
  }

  console.log(`\n🎉 === CALCULATION COMPLETE ===`);
  console.log(`✅ Calculated prices for ${results.length}/${familyTypes.length} family types`);
  
  if (results.length > 0) {
    const prices = results.map(r => r.calculatedPrice);
    const minPrice = Math.min(...prices);
    const maxPrice = Math.max(...prices);
    console.log(`📊 Price range: ₹${minPrice.toLocaleString()} - ₹${maxPrice.toLocaleString()}`);
  }

  return results;
};

// Utility function
export const formatPrice = (price: number): string => {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(price);
}; 