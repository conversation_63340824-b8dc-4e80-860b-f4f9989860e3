<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EMI Options Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .test-btn { background: #8B5CF6; color: white; border: none; padding: 10px 20px; margin: 5px; border-radius: 5px; cursor: pointer; }
        .result { background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; margin: 10px 0; border-radius: 4px; font-family: monospace; font-size: 12px; }
        .success { border-left: 4px solid #28a745; }
        .error { border-left: 4px solid #dc3545; }
        .package-card { border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 8px; }
        .emi-amount { font-size: 1.5rem; font-weight: bold; color: #8B5CF6; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧮 EMI Options Test</h1>
        <p>This page tests EMI option generation and display to ensure no undefined errors occur.</p>
        
        <button class="test-btn" onclick="testEMIGeneration()">🧪 Test EMI Generation</button>
        <button class="test-btn" onclick="testPackageDisplay()">📦 Test Package Display</button>
        
        <div id="result" class="result">Click a button above to start testing...</div>
        
        <div id="packageDisplay"></div>
    </div>

    <!-- Include required scripts -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="js/config.js"></script>
    <script src="js/databaseService.js"></script>

    <script>
        function testEMIGeneration() {
            const resultDiv = document.getElementById('result');
            resultDiv.className = 'result';
            resultDiv.innerHTML = '🧮 Testing EMI generation...\n\n';

            try {
                // Test 1: Package with no EMI data
                const packageWithoutEMI = {
                    id: 'test-1',
                    destination: 'Kashmir',
                    total_price: 45000
                };

                const formatted1 = databaseService.formatPackageForFrontend(packageWithoutEMI);
                resultDiv.innerHTML += '1. Package without EMI data:\n';
                resultDiv.innerHTML += `   ✅ EMI options generated: ${formatted1.emi_options.length}\n`;
                resultDiv.innerHTML += `   ✅ Best EMI: ₹${formatted1.emi_options[0].monthly_amount}/month\n\n`;

                // Test 2: Package with partial data
                const packageWithPartialData = {
                    id: 'test-2',
                    destination: 'Goa',
                    total_price: 32000,
                    family_type_emi_plans: [
                        { emi_months: 6, monthly_amount: 5333 }
                    ]
                };

                const formatted2 = databaseService.formatPackageForFrontend(packageWithPartialData);
                resultDiv.innerHTML += '2. Package with partial EMI data:\n';
                resultDiv.innerHTML += `   ✅ EMI options: ${formatted2.emi_options.length}\n`;
                resultDiv.innerHTML += `   ✅ Monthly amount: ₹${formatted2.emi_options[0].monthly_amount}\n\n`;

                // Test 3: Package with complete data
                const packageWithCompleteData = {
                    id: 'test-3',
                    destination: 'Manali',
                    total_price: 28000,
                    family_type_emi_plans: [
                        {
                            id: 'emi-1',
                            emi_months: 3,
                            monthly_amount: 9333,
                            total_amount: 28000,
                            processing_fee: 500,
                            marketing_label: 'Quick Pay',
                            is_featured: false
                        },
                        {
                            id: 'emi-2',
                            emi_months: 6,
                            monthly_amount: 4667,
                            total_amount: 28000,
                            processing_fee: 800,
                            marketing_label: 'Best Value',
                            is_featured: true
                        }
                    ]
                };

                const formatted3 = databaseService.formatPackageForFrontend(packageWithCompleteData);
                resultDiv.innerHTML += '3. Package with complete EMI data:\n';
                resultDiv.innerHTML += `   ✅ EMI options: ${formatted3.emi_options.length}\n`;
                resultDiv.innerHTML += `   ✅ Featured EMI: ₹${formatted3.emi_options.find(e => e.is_featured).monthly_amount}/month\n\n`;

                resultDiv.className = 'result success';
                resultDiv.innerHTML += '🎉 ALL EMI GENERATION TESTS PASSED!\n';
                resultDiv.innerHTML += '✅ No undefined errors\n';
                resultDiv.innerHTML += '✅ EMI options always generated\n';
                resultDiv.innerHTML += '✅ Safe property access\n';

            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML += `\n❌ ERROR: ${error.message}\n`;
                console.error('EMI generation test error:', error);
            }
        }

        function testPackageDisplay() {
            const packageDisplay = document.getElementById('packageDisplay');
            const resultDiv = document.getElementById('result');
            
            resultDiv.className = 'result';
            resultDiv.innerHTML = '📦 Testing package display...\n\n';

            try {
                // Create test packages with different EMI scenarios
                const testPackages = [
                    {
                        id: 'display-1',
                        title: 'Kashmir Winter Package',
                        destination: 'Kashmir',
                        duration_days: 5,
                        total_price: 45000,
                        emi_options: [
                            {
                                id: 'emi-1',
                                months: 6,
                                monthly_amount: 7500,
                                total_amount: 45000,
                                processing_fee: 1000,
                                label: 'Best Value',
                                is_featured: true
                            }
                        ],
                        inclusions: ['Flights', 'Hotels', 'Meals', 'Sightseeing'],
                        images: ['img/rectangle-14.png'],
                        offer_badge: '15% OFF'
                    },
                    {
                        id: 'display-2',
                        title: 'Goa Beach Package',
                        destination: 'Goa',
                        duration_days: 4,
                        total_price: 32000,
                        emi_options: [], // Empty EMI options to test fallback
                        inclusions: ['Flights', 'Hotels', 'Meals'],
                        images: ['img/rectangle-14-2.png'],
                        offer_badge: 'Best Value'
                    }
                ];

                // Test package card creation
                let displayHTML = '<h3>📦 Test Package Cards:</h3>';
                
                testPackages.forEach((pkg, index) => {
                    try {
                        // Simulate the createPackageCard function logic
                        const emiOptions = pkg.emi_options || [];
                        const bestEMI = emiOptions.find(emi => emi.is_featured) || emiOptions[0] || {
                            monthly_amount: Math.round((pkg.total_price || 45000) / 6),
                            months: 6,
                            total_amount: pkg.total_price || 45000
                        };

                        displayHTML += `
                            <div class="package-card">
                                <h4>${pkg.title}</h4>
                                <p><strong>Destination:</strong> ${pkg.destination}</p>
                                <p><strong>Duration:</strong> ${pkg.duration_days} Days</p>
                                <div class="emi-amount">₹${(bestEMI.monthly_amount || 0).toLocaleString()}/month</div>
                                <p>for ${bestEMI.months || 6} Prepaid EMIs</p>
                                <p><strong>Total:</strong> ₹${(bestEMI.total_amount || 0).toLocaleString()}</p>
                                <p><strong>EMI Options Available:</strong> ${emiOptions.length || 'Using defaults'}</p>
                            </div>
                        `;

                        resultDiv.innerHTML += `${index + 1}. ${pkg.title}:\n`;
                        resultDiv.innerHTML += `   ✅ EMI display: ₹${(bestEMI.monthly_amount || 0).toLocaleString()}/month\n`;
                        resultDiv.innerHTML += `   ✅ No undefined errors\n\n`;

                    } catch (error) {
                        resultDiv.innerHTML += `${index + 1}. ${pkg.title}:\n`;
                        resultDiv.innerHTML += `   ❌ Error: ${error.message}\n\n`;
                    }
                });

                packageDisplay.innerHTML = displayHTML;

                resultDiv.className = 'result success';
                resultDiv.innerHTML += '🎉 PACKAGE DISPLAY TESTS PASSED!\n';
                resultDiv.innerHTML += '✅ All packages display correctly\n';
                resultDiv.innerHTML += '✅ EMI amounts show properly\n';
                resultDiv.innerHTML += '✅ No undefined property errors\n';

            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML += `\n❌ ERROR: ${error.message}\n`;
                console.error('Package display test error:', error);
            }
        }

        // Auto-run basic test on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                document.getElementById('result').innerHTML = 'Page loaded. Ready to test EMI functionality.';
            }, 1000);
        });
    </script>
</body>
</html>
